import fsd from '@feature-sliced/steiger-plugin';
import { defineConfig } from 'steiger';

export default defineConfig([
	...fsd.configs.recommended,
	{
		// Ignore legacy code and generated files
		ignores: ['_legacy/**', '**/generated.tsx'],
	},
	{
		files: ['./src/**'],
		rules: {
			// Allow insignificant slices
			'fsd/insignificant-slice': 'off',
		},
	},
	{
		files: ['./src/shared/domain/**'],
		rules: {
			'fsd/no-public-api-sidestep': 'off',
			'fsd/public-api': 'off',
		},
	},
]);

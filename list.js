const fs = require('fs');
const path = require('path');

function listFiles(dir, baseDir = dir) {
	try {
		const files = fs.readdirSync(dir);

		files.forEach((file) => {
			const fullPath = path.join(dir, file);
			const relativePath = path.relative(baseDir, fullPath);

			if (fs.statSync(fullPath).isDirectory()) {
				listFiles(fullPath, baseDir); // Recursively list files in subdirectories
			} else {
				console.log(relativePath); // Print file relative path
			}
		});
	} catch (error) {
		console.error(`Error reading directory: ${dir}`, error);
	}
}

const targetDir = process.argv[2] || '.'; // Get directory from command line argument, default to current directory
listFiles(path.resolve(targetDir));

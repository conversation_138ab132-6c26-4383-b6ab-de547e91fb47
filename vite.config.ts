import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react';
import path from 'path';
import { defineConfig } from 'vite';

// https://vitejs.dev/config/
const plugins = [
	react({
		babel: {
			plugins: [
				[
					'babel-plugin-styled-components',
					{
						displayName: true,
						fileName: false,
						sourceMap: true,
					},
				],
			],
		},
	}),
];

if (process.env.VITE_SENTRY_AUTH_TOKEN) {
	plugins.push(
		sentryVitePlugin({
			org: 'sentry',
			project: 'esw-web',
			authToken: process.env.VITE_SENTRY_AUTH_TOKEN,
			sourcemaps: {
				filesToDeleteAfterUpload: ['**/*.js.map'],
			},
		}),
	);
}

export default defineConfig({
	resolve: {
		alias: {
			'@app': path.resolve(__dirname, './src/app'),
			'@pages': path.resolve(__dirname, './src/pages'),
			'@widgets': path.resolve(__dirname, './src/widgets'),
			'@features': path.resolve(__dirname, './src/features'),
			'@entities': path.resolve(__dirname, './src/entities'),
			'@shared': path.resolve(__dirname, './src/shared'),
		},
	},
	build: {
		chunkSizeWarningLimit: 1000,
		sourcemap: true,
		rollupOptions: {
			output: {
				manualChunks(id) {
					if (id.includes('node_modules')) {
						return id.toString().split('node_modules/')[1].split('/')[0].toString();
					}
				},
			},
		},
	},
	plugins: plugins,
	server: {
		watch: {
			usePolling: true,
		},
		host: true,
		port: 5174,
		strictPort: true,
	},
});

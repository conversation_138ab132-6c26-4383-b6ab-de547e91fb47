const DateFns = require('date-fns');
const DateFnsTz = require('date-fns-tz');

console.log(DateFns);
console.log(DateFnsTz);


// function formatEventTime(
// 	rawMillis,
// 	tz,
// 	pattern = 'yyyy-MM-dd HH:mm'
// ) {
// 	return DateFnsTz.formatInTimeZone(rawMillis, tz, pattern);
// }

const res = DateFnsTz.formatInTimeZone('1746057600000', 'UTC', 'yyyy-MM-dd HH:mm:ss zzz');
console.log(res);

// const match_start = 1711872000000;
// const resUTC = formatEventTime(match_start, 'UTC', 'HH:mm');
// const resNewYork = formatEventTime(match_start, 'America/New_York', 'HH:mm');
// console.log(resUTC);
// console.log(resNewYork);
//
//



// const match_start = 1711872000000;
// const match_start_date = new Date(match_start);
// console.log(match_start_date, match_start_date.getUTCHours());
//
// const now = Date.now();
// const startOfMyDay = DateFns.startOfDay(now);
// console.log(startOfMyDay, startOfMyDay.getUTCHours());
//
//
//
//





// const raw_date_number = 1453028400000;
// const tz_date_number = 1453046400000;
// console.log(DateFnsTz.formatInTimeZone(raw_date_number, 'UTC', 'HH:mm'));
// console.log(DateFnsTz.formatInTimeZone(tz_date_number, 'America/New_York', 'HH:mm'));
//
// const now = Date.now();
// const startOfMyDay = DateFns.startOfDay(now);
// const endOfMyDay = DateFns.endOfDay(now);
// console.log(startOfMyDay, endOfMyDay);
//
// const startOfNewYorkDay = DateFnsTz.fromZonedTime(startOfMyDay, 'America/New_York');
// console.log('MY DAY', startOfMyDay);
// console.log('NEW YORK', startOfNewYorkDay);
//
// console.log(DateFnsTz.formatInTimeZone(startOfNewYorkDay, 'America/New_York', 'yyyy-MM-dd HH:mm:ss zzz'));
//
// console.log('TEST', new Date('2016-01-17 11:00:00'));

// const startOfNewYorkDay = DateFns.startOfDay();
// const endOfNewYorkDay = DateFns.endOfDay(now, { timeZone: 'America/New_York' });
// console.log(startOfNewYorkDay, endOfNewYorkDay);



function startOfDayUTC(date = new Date()) {
	// step 1 – pretend the current instant is already in UTC
	const asUtc = utcToZonedTime(date, 'UTC');
	// step 2 – chop the clock to midnight *in that same zone*
	const midnightInUtcZone = startOfDay(asUtc);
	// step 3 – re-express as a real UTC Date object
	return zonedTimeToUtc(midnightInUtcZone, 'UTC');
}
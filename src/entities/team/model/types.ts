import type {
	DivisionTeamsStandingQuery,
	FavoriteTeamsStandingQuery,
	PaginatedDivisionTeamsQuery,
	QualifiedTeamsQuery,
} from '../api';

export type PaginatedDivisionTeam =
	PaginatedDivisionTeamsQuery['paginatedDivisionTeams']['items'][number];

export type DivisionTeamWithStanding = DivisionTeamsStandingQuery['divisionTeamsStanding'][number];

export type QualifiedTeam = QualifiedTeamsQuery['qualifiedTeams'][number];

export type FavoriteTeam = FavoriteTeamsStandingQuery['favoriteTeams'][number];

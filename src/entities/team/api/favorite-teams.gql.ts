import { gql } from '@apollo/client';

export const FAVORITE_TEAMS_STANDING = gql`
	query FavoriteTeamsStanding($eswId: ID!, $teamsIds: [ID!]!, $search: String) {
		favoriteTeams(eventKey: $eswId, teamsIds: $teamsIds, search: $search) {
			club_id
			team_id
			team_name
			team_code
			division_id
			division_name
			next_match {
				secs_start
				external {
					pool_bracket_info {
						is_pool
						uuid
					}
					court_info {
						short_name
					}
				}
			}
			division_standing {
				matches_won
				matches_lost
				sets_won
				sets_lost
				sets_pct
				points_ratio
				rank
				seed
				heading
				heading_priority
			}
		}
	}
`;

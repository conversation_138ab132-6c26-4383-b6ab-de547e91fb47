import { gql } from '@apollo/client';

export const DIVISION_TEAMS_STANDING_QUERY = gql`
	query DivisionTeamsStanding($eswId: ID!, $divisionId: ID!) {
		divisionTeamsStanding(eventKey: $eswId, divisionId: $divisionId) {
			team_id
			team_name
			team_code
			extra {
				show_previously_accepted_bid
				show_accepted_bid
			}
			division_standing {
				matches_won
				matches_lost
				sets_won
				sets_lost
				sets_pct
				points_ratio
				points
				rank
				seed
				heading
				heading_priority
			}
		}
	}
`;

import { gql } from '@apollo/client';

export const PAGINATED_DIVISION_TEAMS_QUERY = gql`
	query PaginatedDivisionTeams(
		$eswId: ID!
		$divisionId: ID!
		$page: Float!
		$pageSize: Float!
		$search: String
	) {
		paginatedDivisionTeams(
			eventKey: $eswId
			divisionId: $divisionId
			page: $page
			pageSize: $pageSize
			search: $search
		) {
			items {
				team_id
				club_id
				team_name
				division_id
				next_match {
					secs_start
					external {
						opponent_display_name
						court_info {
							short_name
						}
						pool_bracket_info {
							is_pool
							uuid
						}
					}
				}
				division_standing {
					matches_won
					matches_lost
					sets_won
					sets_lost
					rank
				}
			}
			page_info {
				page
				page_size
				page_count
				item_count
			}
		}
	}
`;

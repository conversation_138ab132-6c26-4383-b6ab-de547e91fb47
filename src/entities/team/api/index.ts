export {
	usePaginatedDivisionTeamsLazyQuery,
	useDivisionTeamsStandingQuery,
	useQualifiedTeamsQuery,
	useFavoriteTeamsStandingQuery,
} from '@shared/graphql';
export type {
	PaginatedDivisionTeamsQuery,
	PaginatedDivisionTeamsQueryVariables,
	DivisionTeamsStandingQuery,
	DivisionTeamsStandingQueryVariables,
	QualifiedTeamsQuery,
	QualifiedTeamsQueryVariables,
	FavoriteTeamsStandingQuery,
	FavoriteTeamsStandingQueryVariables,
} from '@shared/graphql';

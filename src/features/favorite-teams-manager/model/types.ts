import { Club, Team } from '@shared/graphql';

/**
 * Minimal representation of a team and club entities, used as a reference.
 * Ensures that the necessary fields are available for favorite operations.
 */
export type FavoriteTeamRef = Pick<Team, 'team_id' | 'club_id'>;
export type FavoriteClubRef = Pick<Club, 'roster_club_id'> & { teams: FavoriteTeamRef[] };

export type FavoriteTeamsManagerReturnType = {
	favoriteTeamsIds: string[];
	toggleTeamFavorite: (team: FavoriteTeamRef) => void;
	toggleClubTeamsFavorite: (club: FavoriteClubRef) => void;
};

export enum FavoriteStatus {
	None = 'none',
	Partial = 'partial',
	Full = 'full',
}

import { intersection } from 'lodash';

import { FavoriteStatus, FavoriteTeamRef } from '@features/favorite-teams-manager';

/*
 * Determines the favorite status of a group of teams based on their IDs and the IDs of favorite teams.
 */
export const getGroupFavoriteStatus = (
	teamsRefs: FavoriteTeamRef[],
	favoriteTeamsIds: string[],
): FavoriteStatus => {
	if (!teamsRefs.length || !favoriteTeamsIds.length) {
		return FavoriteStatus.None;
	}

	const teamsIds = teamsRefs.map((team) => team.team_id);
	const commonIdsCount = intersection(teamsIds, favoriteTeamsIds).length;
	if (commonIdsCount === teamsIds.length) {
		return FavoriteStatus.Full; // If all teams are favorite
	} else if (!commonIdsCount) {
		return FavoriteStatus.None; // If no teams are favorite
	} else {
		return FavoriteStatus.Partial; // If some teams are favorite
	}
};

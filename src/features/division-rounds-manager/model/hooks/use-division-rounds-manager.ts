import { useMemo } from 'react';

import { useEventOverview } from '@entities/event-overview';

import { findDivisionById } from '@shared/domain/division';
import {
	RoundGroup,
	RoundGroupKeyType,
	getRoundGroupKey,
	groupAndMergeRounds,
} from '@shared/domain/round';

import {
	DivisionRoundsManagerParams,
	DivisionRoundsManagerReturnType,
	RoundDetails,
} from '../types';

export const useDivisionRoundsManager = ({
	eswId,
	divisionId,
}: DivisionRoundsManagerParams): DivisionRoundsManagerReturnType => {
	const { divisions, loading } = useEventOverview(eswId);

	return useMemo(() => {
		const division = findDivisionById(divisions, divisionId);
		const rounds = division?.rounds ?? null;
		const groupingResults = rounds ? groupAndMergeRounds(rounds) : null;

		return {
			rounds,
			roundsGroups: groupingResults?.groupsMap
				? Array.from(groupingResults.groupsMap.values())
				: null,
			getRoundGroupKeyByRoundId(roundId: string): RoundGroupKeyType | null {
				const round = groupingResults?.roundsMap.get(roundId);
				if (!round) return null;
				return getRoundGroupKey(round);
			},
			getRoundGroupByGroupKey(groupKey: RoundGroupKeyType): RoundGroup<RoundDetails> | null {
				return groupingResults?.groupsMap.get(groupKey) ?? null;
			},
			getRoundsByGroupKey(groupKey: RoundGroupKeyType): RoundDetails[] | null {
				if (!groupingResults?.groupKeysRoundsMap) return null;
				return groupingResults.groupKeysRoundsMap.get(groupKey) ?? null;
			},
			loading,
		};
	}, [divisions, divisionId, loading]);
};

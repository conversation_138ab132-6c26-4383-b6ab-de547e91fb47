import type { Bracket, Pool, Round } from '@shared/graphql';

export type RoundRef = Pick<Round, 'short_name' | 'uuid'>;

export type GroupableRound = RoundRef &
	Pick<Round, 'uuid' | 'first_match_start' | 'last_match_start'>;
export type RoundGroup<T extends GroupableRound> = Omit<T, 'uuid'> & { groupKey: string };

export type RoundGroupKeyType = string;

export type RoundGroupablePoolBracket = Pick<Pool & Bracket, 'round_id'>;

export type RoundWithTimeRange = Pick<Round, 'first_match_start' | 'last_match_start'>;

export type RoundsGroupingResult<T extends GroupableRound> = {
	groupsMap: Map<RoundGroupKeyType, RoundGroup<T>>; // group key ⇒ merged round info
	roundsMap: Map<string, T>; // round_id ⇒ round
	groupKeysRoundsMap: Map<RoundGroupKeyType, T[]>; // group key ⇒ rounds in this group
};

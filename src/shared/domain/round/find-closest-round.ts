import { differenceInCalendarDays } from 'date-fns';

import { startOfDayUTC } from '@shared/lib/time';

import { RoundWithTimeRange } from './types';

/**
 * Finds the round whose day-range is closest to “now”.
 * Rules:
 *   • Today inside round’s [firstDay, lastDay]     → choose that round  (diff = 0)
 *   • Otherwise distance = days to nearest edge.
 *   • Ties favour the *future* (positive diff).
 */
export const findClosestRound = <T extends RoundWithTimeRange>(rounds: T[]): T | null => {
	if (!rounds.length) return null;

	// Utilizing UTC as all dates are ignoring timezones and represented as a naive time in milliseconds
	const today = startOfDayUTC();

	let best: { round: T; diff: number } | null = null;

	for (const round of rounds) {
		const first = round.first_match_start;
		if (!first) continue; // safeguard

		const last = round.last_match_start ?? first;

		const firstDay = startOfDayUTC(first);
		const lastDay = startOfDayUTC(last);

		let diff: number;

		if (today < firstDay) {
			// Round entirely in the future → distance to its first day (positive)
			diff = differenceInCalendarDays(firstDay, today);
		} else if (today > lastDay) {
			// Round entirely in the past  → negative distance to its last day
			diff = -differenceInCalendarDays(today, lastDay);
		} else {
			// Today is within the round’s span
			diff = 0;
		}

		if (
			best === null ||
			Math.abs(diff) < Math.abs(best.diff) ||
			(Math.abs(diff) === Math.abs(best.diff) && diff > 0) // tie → future wins
		) {
			best = { round, diff };
		}
	}

	return best?.round ?? null;
};

import { getRoundGroupKey } from '@shared/domain/round';
import { getMaxTime, getMinTime } from '@shared/lib/time';

import { GroupableRound, RoundGroup, RoundGroupKeyType, RoundsGroupingResult } from './types';

/*
 * Group rounds by their group key
 */
export const groupAndMergeRounds = <T extends GroupableRound>(
	rounds: T[],
): RoundsGroupingResult<T> => {
	const roundsMap = new Map<string, T>(); // round_id ⇒ round
	const groupsMap = new Map<RoundGroupKeyType, RoundGroup<T>>(); // group key ⇒ merged round info
	const groupKeysRoundsMap = new Map<RoundGroupKeyType, T[]>(); // group key ⇒ rounds in this group

	rounds.forEach((round) => {
		const roundId = round.uuid;
		const groupKey = getRoundGroupKey(round);

		if (!groupsMap.has(groupKey)) {
			groupsMap.set(groupKey, { ...round, groupKey }); // if the group doesn't exist, create it
		} else {
			const existing = groupsMap.get(groupKey)!;
			groupsMap.set(groupKey, {
				...existing, // if the group exists, merge the round timestamps
				first_match_start: getMinTime(existing.first_match_start, round.first_match_start),
				last_match_start: getMaxTime(existing.last_match_start, round.last_match_start),
			});
		}

		roundsMap.set(roundId, round);
		if (!groupKeysRoundsMap.has(groupKey)) {
			groupKeysRoundsMap.set(groupKey, []);
		}
		groupKeysRoundsMap.get(groupKey)!.push(round);
	});

	return { groupsMap, roundsMap, groupKeysRoundsMap };
};

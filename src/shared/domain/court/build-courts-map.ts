import { CourtWithId } from './types';

type COURTS_MAP_MEMO_TYPE<T extends CourtWithId> = WeakMap<T[], Map<string, T>>;

const COURTS_MAP_MEMO = new WeakMap();

export const buildCourtsMap = <T extends CourtWithId>(courts: T[]): Map<string, T> => {
	const memoizedMap = COURTS_MAP_MEMO as COURTS_MAP_MEMO_TYPE<T>;
	const existingMap = memoizedMap.get(courts);
	if (existingMap) {
		return existingMap;
	}

	const courtsMap = new Map<string, T>();
	courts.forEach((court) => courtsMap.set(court.uuid, court));
	memoizedMap.set(courts, courtsMap);
	return courtsMap;
};

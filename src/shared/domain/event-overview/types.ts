import { Division, MatchesTimeRange } from '@shared/graphql';

export type MatchesTimeRangeRef = Pick<MatchesTimeRange, 'day' | 'start_time' | 'end_time'>;

export type DivisionWithTimeRangeRef<TR extends MatchesTimeRangeRef> = Pick<
	Division,
	'division_id'
> & {
	matches_time_ranges: TR[];
};

export type DaysMatchesTimeRangesMap<TR extends MatchesTimeRangeRef> = Map<string, TR>;
export type DivisionsDaysRangesMap<TR extends MatchesTimeRangeRef> = Map<
	string,
	DaysMatchesTimeRangesMap<TR>
>;

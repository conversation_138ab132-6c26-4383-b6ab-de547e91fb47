import { ALL_DIVISIONS_KEY } from '@shared/config';
import { SelectOption } from '@shared/model';

import { DivisionWithNameRef, DivisionsListOptionsParams } from './types';

const ALL_DIVISIONS_OPTION: SelectOption = {
	id: ALL_DIVISIONS_KEY,
	label: 'All Divisions',
};

export const buildDivisionsListOptions = <T extends DivisionWithNameRef>(
	divisions: T[],
	params: DivisionsListOptionsParams,
) => {
	const { withAllDivisionOption, shortName } = params;

	const options: SelectOption[] = divisions.map((division) => ({
		id: division.division_id,
		label: (shortName ? division.short_name : division.name) || division.division_id,
	}));

	if (withAllDivisionOption) {
		options.unshift(ALL_DIVISIONS_OPTION);
	}

	return options;
};

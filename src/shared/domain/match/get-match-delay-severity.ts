import { MatchDelaySeverity, MatchWithTimeRange } from './types';

const WARNING_DELAY = 15 * 60 * 1000; // 15 min
const CRITICAL_DELAY = 30 * 60 * 1000; // 30 min

const SEVERITY_ORDER = {
	[MatchDelaySeverity.None]: 0,
	[MatchDelaySeverity.Warning]: 1,
	[MatchDelaySeverity.Critical]: 2,
};

/**
 * Calculates the delay severity of a match based on its end and finished timestamps.
 * @param match - The match object containing time range properties.
 * @returns MatchDelaySeverity - The severity of the match delay.
 */
export const getMatchDelaySeverity = <T extends MatchWithTimeRange>(
	match: T,
): MatchDelaySeverity => {
	const { secs_end, secs_finished } = match;
	if (!secs_finished || !secs_end) return MatchDelaySeverity.None;

	const diff = secs_finished - secs_end; // ms late
	if (diff < 0) return MatchDelaySeverity.None;

	if (diff > CRITICAL_DELAY) return MatchDelaySeverity.Critical;
	if (diff > WARNING_DELAY) return MatchDelaySeverity.Warning;
	return MatchDelaySeverity.None;
};

/**
 * Returns the maximum severity from a list of severities.
 * @param severities - An array of MatchDelaySeverity values.
 * @returns The maximum severity based on the defined order.
 */
export const maxDelaySeverity = (...severities: MatchDelaySeverity[]): MatchDelaySeverity => {
	return severities.reduce((max, current) => {
		return SEVERITY_ORDER[current] > SEVERITY_ORDER[max] ? current : max;
	}, MatchDelaySeverity.None);
};

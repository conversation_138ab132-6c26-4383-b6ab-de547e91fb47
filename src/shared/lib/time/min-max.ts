/**
 * Returns the minimum time value from the provided array of times.
 * Filters out null, undefined, and NaN values.
 *
 * @param times - Array of time values (can include null, undefined, or NaN)
 * @returns The minimum time value, or null if no valid times are provided
 */
export const getMinTime = (...times: (number | null | undefined)[]): number | null => {
	const validTimes = times.filter((t) => t != null && !isNaN(t)) as number[];
	return validTimes.length > 0 ? Math.min(...validTimes) : null;
};

/**
 * Returns the maximum time value from the provided array of times.
 * Filters out null, undefined, and NaN values.
 *
 * @param times - Array of time values (can include null, undefined, or NaN)
 * @returns The maximum time value, or null if no valid times are provided
 */
export const getMaxTime = (...times: (number | null | undefined)[]): number | null => {
	const validTimes = times.filter((t) => t != null && !isNaN(t)) as number[];
	return validTimes.length > 0 ? Math.max(...validTimes) : null;
};

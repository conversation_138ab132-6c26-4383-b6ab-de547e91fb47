import { type DateArg, eachMinuteOfInterval } from 'date-fns';

/**
 * Generates an array of Date objects at regular intervals between start and end dates
 * @param startDate - The start date
 * @param endDate - The end date
 * @param stepMinutes - The interval in minutes
 * @returns Array of Date objects
 */
export const getIntervalsInRange = (
	startDate: DateArg<Date>,
	endDate: DateArg<Date>,
	stepMinutes: number,
): Date[] => {
	return eachMinuteOfInterval({ start: startDate, end: endDate }, { step: stepMinutes });
};

import { formatInTimeZone } from 'date-fns-tz';

export const enum DateTimeFormat {
	TwentyFourHour = 'HH:mm', // 18:45
	TwentyFourHourLeadingZero = 'hh:mm', // 06:45
	TwelveHour = 'h:mm aa', // 6:45 PM
	TwelveHourLeadingZero = 'hh:mm aa', // 06:45 PM
	TwelveHourCompact = 'h:mmaaa', // 6:45PM (no space)
	TwelveHourSimple = 'h a', // 6 AM
	MonthShortDayYear = 'MMM d, yyyy', // May 1, 2025
	MonthShortDay = 'MMM d', // May 1
	WeekdayShortMonthDay = 'eee MM/dd', // Mon 05/01
	WeekdayShort = 'EEE', // Mon
	WeekdayFull = 'EEEE', // Monday
	WeekdayShortTime = 'EEE h:mm aa', // Mon 6:45 PM
}

/**
 * Formats a data in UTC timezone to a specified time format
 * @param date - Date object or timestamp
 * @param timeFormat - Format string (default: 'HH:mm')
 * @returns Formatted time string
 */
export const formatInUTC = (
	date: Date | string | number,
	timeFormat: DateTimeFormat = DateTimeFormat.TwentyFourHour,
): string => {
	return formatInTimeZone(date, 'UTC', timeFormat);
};

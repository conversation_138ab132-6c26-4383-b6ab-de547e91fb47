import { type DateArg, differenceInCalendarDays, toDate } from 'date-fns';

/**
 * Receive anything that represents a moment – a Date, an ISO-8601 string,
 * or a number – and return a Date set to 00 : 00 UTC on the
 * same calendar day.
 */
export const startOfDayUTC = (value?: DateArg<Date>): Date => {
	const date = value ? toDate(value) : new Date();
	return new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()));
};

/**
 * Set a specific time of day (in UTC) on a given date.
 * This function takes a date and a time string in the format "HH:mm"
 * and returns a new Date object set to that time on the specified date.
 * @param day
 * @param time
 */
export const atTimeOfDayUTC = (day: DateArg<Date>, time: string): Date => {
	const [hours, minutes] = time.split(':').map(Number);
	const date = toDate(day);
	date.setUTCHours(hours, minutes, 0, 0);
	return date;
};

/**
 * Find the closest day from a list of days to a given date.
 * This function compares the days in the list with the provided date
 * and returns the one that is closest, preferring future days in case of a tie.
 * @param days
 * @param closeTo
 */
export const findClosestDay = <T extends DateArg<Date>, M extends DateArg<Date>>(
	days: T[],
	closeTo?: M,
): T | null => {
	if (!days.length) return null;

	const today = startOfDayUTC(closeTo);

	let best: { day: T; diff: number } | null = null;

	for (const day of days) {
		const dayDate = startOfDayUTC(day);
		const diff = differenceInCalendarDays(dayDate, today);

		if (
			best === null ||
			Math.abs(diff) < Math.abs(best.diff) ||
			(Math.abs(diff) === Math.abs(best.diff) && diff > 0) // tie → future wins
		) {
			best = { day, diff };
		}
	}

	return best?.day ?? null;
};

import { type LoaderFunctionArgs, redirect } from 'react-router';

import { atTimeOfDayUTC } from '@shared/lib/time';
import { isNumericId, isNumericIndex, isTimeFormat } from '@shared/lib/validators';

export const courtGridGuard = ({ params }: LoaderFunctionArgs) => {
	const { startTime, endTime } = params;

	if (
		!isNumericId(params.divisionId) ||
		!isNumericIndex(params.dayIndex) ||
		!isTimeFormat(startTime) ||
		!isTimeFormat(endTime)
	) {
		return redirect('/events');
	}

	// Validate that startTime is before endTime
	const now = new Date();
	if (atTimeOfDayUTC(now, startTime) >= atTimeOfDayUTC(now, endTime)) {
		return redirect('/events');
	}

	return null;
};

import { type LoaderFunctionArgs, redirect } from 'react-router';

// TODO Import this enum from another location
enum EventsTabs {
	Current = 'current',
	Future = 'future',
	Past = 'past',
}

export const eventsGuard = ({ params, request }: LoaderFunctionArgs) => {
	const { tab } = params;
	if (!tab || !Object.values(EventsTabs).includes(tab as EventsTabs)) {
		// If no tab is provided, redirect to the current tab
		throw redirect(`/events/current${new URL(request.url).search}`);
	}
	return null;
};

import { Helmet } from 'react-helmet';

import { AppProviders } from './providers';

const App = () => {
	return (
		<AppProviders>
			<Helmet>
				<link
					href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
					rel="stylesheet"
				/>
			</Helmet>
			{/*<p>*/}
			{/*	Code from here goes before the router. Locate "react-toastify" or any similar things here*/}
			{/*</p>*/}
		</AppProviders>
	);
};

export default App;

import {
	FavoriteStatus,
	FavoriteStatusIcon,
	FavoriteTeamRef,
} from '@features/favorite-teams-manager';

import { FavoriteTeam } from '@entities/team';

import { DateTimeFormat, formatInUTC } from '@shared/lib/time';

import {
	CourtName,
	DivisionName,
	MatchTime,
	NextMatchInfo,
	StandingInfo,
	TeamCard,
	TeamCode,
	TeamDetails,
	TeamInfo,
	TeamName,
} from './styled';

export type Props = {
	team: FavoriteTeam;
	favoriteTeamsIds: string[];
	toggleTeamFavorite: (team: FavoriteTeamRef) => void;
	onTeamClick?: (team: FavoriteTeam) => void;
};

export const FavoriteTeamItem = ({ team, toggleTeamFavorite, onTeamClick }: Props) => {
	const handleToggleFavorite = (e: React.MouseEvent) => {
		e.stopPropagation();
		toggleTeamFavorite(team);
	};

	const handleTeamClick = () => {
		if (onTeamClick) {
			onTeamClick(team);
		}
	};

	const getCourtName = (courtShortName?: string | null) => {
		if (!courtShortName) return '';
		return courtShortName.startsWith('Court') ? courtShortName : `Court ${courtShortName}`;
	};

	return (
		<TeamCard>
			<TeamInfo>
				<TeamName onClick={handleTeamClick}>{team.team_name}</TeamName>
				<TeamDetails>
					<DivisionName>{team.division_name}</DivisionName>
					<TeamCode>{team.team_code}</TeamCode>
				</TeamDetails>
			</TeamInfo>

			{team.next_match ? (
				<NextMatchInfo>
					<MatchTime>
						{formatInUTC(team.next_match.secs_start!, DateTimeFormat.WeekdayShortTime)}
					</MatchTime>
					<CourtName>{getCourtName(team.next_match.external?.court_info?.short_name)}</CourtName>
				</NextMatchInfo>
			) : (
				<StandingInfo>
					{team.division_standing && (
						<>
							{team.division_standing.rank && <span>Rank: {team.division_standing.rank}</span>}
							{team.division_standing.seed && <span>Seed: {team.division_standing.seed}</span>}
						</>
					)}
				</StandingInfo>
			)}

			<FavoriteStatusIcon status={FavoriteStatus.Full} onClick={handleToggleFavorite} />
		</TeamCard>
	);
};

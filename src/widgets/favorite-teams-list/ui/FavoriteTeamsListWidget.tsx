import { useFavoriteTeamsManager } from '@features/favorite-teams-manager';

import { FavoriteTeam } from '@entities/team';

import { useFavoriteTeams } from '../model';
import { FavoriteTeamItem } from './FavoriteTeamItem';
import { LoadingWrapper, NoTeamsMessage, TeamItem, TeamsList, WidgetWrapper } from './styled';

export type Props = {
	eswId: string;
	search?: string | null;
	onTeamClick?: (team: FavoriteTeam) => void;
};

export const FavoriteTeamsListWidget = ({ eswId, search, onTeamClick }: Props) => {
	const { favoriteTeamsIds, toggleTeamFavorite } = useFavoriteTeamsManager(eswId);
	const { teams, loading } = useFavoriteTeams({
		eswId,
		favoriteTeamsIds,
		search,
	});

	if (loading && (!teams || teams.length === 0)) {
		return (
			<LoadingWrapper>
				<div>Loading favorite teams...</div>
			</LoadingWrapper>
		);
	}

	if (!loading && (!teams || teams.length === 0)) {
		return <NoTeamsMessage>No favorite teams found</NoTeamsMessage>;
	}

	return (
		<WidgetWrapper>
			<TeamsList>
				{teams.map((team) => (
					<TeamItem key={team.team_id}>
						<FavoriteTeamItem
							team={team}
							favoriteTeamsIds={favoriteTeamsIds}
							toggleTeamFavorite={toggleTeamFavorite}
							onTeamClick={onTeamClick}
						/>
					</TeamItem>
				))}
			</TeamsList>
		</WidgetWrapper>
	);
};

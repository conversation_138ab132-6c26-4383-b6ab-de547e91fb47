import styled from 'styled-components';

export const WidgetWrapper = styled.div`
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 1rem;
`;

export const EventsList = styled.ul`
	list-style: none;
	padding: 0;
	margin: 0;
	max-height: 70vh;
	overflow-y: auto;
`;

export const EventItem = styled.li`
	margin: 0 0 1rem;
`;

export const LoadingWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
`;

export const NoEventsMessage = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	font-size: 1rem;
	color: #637381;
`;

export const EventCard = styled.div`
	border-radius: 4px;
	border: 1px solid #dfe3e8;
	padding: 16px;
	cursor: pointer;
	transition: box-shadow 0.2s ease;

	&:hover {
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

		.event-card__header {
			text-decoration: underline;
		}
	}
`;

export const EventCardHeader = styled.div`
	display: flex;
	align-items: flex-start;
	gap: 12px;
	margin-bottom: 8px;
`;

export const EventLogo = styled.img`
	width: 40px;
	height: 40px;
	border-radius: 4px;
	object-fit: cover;
	flex-shrink: 0;
`;

export const EventInfo = styled.div`
	flex: 1;
`;

export const EventName = styled.h3`
	margin: 0 0 4px 0;
	font-size: 16px;
	line-height: 24px;
	font-weight: 700;
	color: #212b36;
`;

export const EventDetails = styled.p`
	margin: 0;
	font-size: 14px;
	line-height: 22px;
	color: #637381;
`;

export const EventBadge = styled.div`
	display: flex;
	align-items: center;
	gap: 4px;
	margin-top: 8px;
`;

export const ScheduleIcon = styled.img`
	width: 16px;
	height: 16px;
`;

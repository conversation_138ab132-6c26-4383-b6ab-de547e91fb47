import { useCallback, useRef } from 'react';

import { PaginatedEvent, PaginatedEventsParams, usePaginatedEvents } from '../model';
import { EventCard } from './EventCard';
import { EventItem, EventsList, LoadingWrapper, NoEventsMessage, WidgetWrapper } from './styled';

export type Props = PaginatedEventsParams & {
	onEventClick?: (event: PaginatedEvent) => void;
};

export const EventsListWidget = (props: Props) => {
	const { onEventClick } = props;
	const { data: events, loading, fetchNext } = usePaginatedEvents(props);

	const listRef = useRef<HTMLUListElement>(null);

	// Handle scroll to load more events
	const handleScroll = useCallback(() => {
		if (!listRef.current) return;

		const { scrollTop, scrollHeight, clientHeight } = listRef.current;

		// If we're near the bottom, load more
		if (scrollTop + clientHeight >= scrollHeight - 100 && !loading) {
			fetchNext();
		}
	}, [fetchNext, loading]);

	// Add scroll event listener
	const handleScrollEvent = useCallback(() => {
		handleScroll();
	}, [handleScroll]);

	if (loading && events.length === 0) {
		return (
			<WidgetWrapper>
				<LoadingWrapper>
					<div>Loading events...</div>
				</LoadingWrapper>
			</WidgetWrapper>
		);
	}

	if (!loading && events.length === 0) {
		return (
			<WidgetWrapper>
				<NoEventsMessage>No events found</NoEventsMessage>
			</WidgetWrapper>
		);
	}

	return (
		<WidgetWrapper>
			<EventsList ref={listRef} onScroll={handleScrollEvent}>
				{events.map((event) => (
					<EventItem key={event.event_id}>
						<EventCard event={event} onEventClick={onEventClick} />
					</EventItem>
				))}
				{loading && (
					<LoadingWrapper>
						<div>Loading more...</div>
					</LoadingWrapper>
				)}
			</EventsList>
		</WidgetWrapper>
	);
};

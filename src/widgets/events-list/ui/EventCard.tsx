import calendarIcon from '@shared/assets/icons/calendar-icon.svg';
import logoPlaceholder from '@shared/assets/icons/logo-team-placeholder.png';
import { DateTimeFormat, formatInUTC } from '@shared/lib/time';

import { PaginatedEvent } from '../model';
import {
	EventBadge,
	EventCardHeader,
	EventDetails,
	EventInfo,
	EventLogo,
	EventName,
	ScheduleIcon,
	EventCard as StyledEventCard,
} from './styled';

type Props = {
	event: PaginatedEvent;
	onEventClick?: (event: PaginatedEvent) => void;
};

// Host URL for images - following the pattern from legacy code
const HOST =
	import.meta.env.VITE_APP_ENV === 'development'
		? import.meta.env.VITE_DEV_SERVER
		: import.meta.env.VITE_PROD_SERVER;

export const EventCard = ({ event, onEventClick }: Props) => {
	const { long_name, city, state, date_start, schedule_published, small_logo } = event;

	const handleClick = () => {
		onEventClick?.(event);
	};

	const handleLogoError = (e: React.SyntheticEvent<HTMLImageElement>) => {
		const target = e.target as HTMLImageElement;
		target.src = logoPlaceholder;
	};

	return (
		<StyledEventCard onClick={handleClick}>
			<EventCardHeader>
				{small_logo && (
					<EventLogo src={`${HOST}${small_logo}`} alt="event logo" onError={handleLogoError} />
				)}
				<EventInfo>
					<EventName className="event-card__header">{long_name}</EventName>
					<EventDetails>
						{/* TODO: Return the date_start as a number from the API to avoid unnecessary casting */}
						{date_start && formatInUTC(Number(date_start), DateTimeFormat.MonthShortDayYear)} {city}
						{state && `, ${state}`}
					</EventDetails>
				</EventInfo>
			</EventCardHeader>
			{schedule_published && (
				<EventBadge>
					<ScheduleIcon src={calendarIcon} alt="schedule published" />
					<span style={{ fontSize: '12px', color: '#637381' }}>Schedule Published</span>
				</EventBadge>
			)}
		</StyledEventCard>
	);
};

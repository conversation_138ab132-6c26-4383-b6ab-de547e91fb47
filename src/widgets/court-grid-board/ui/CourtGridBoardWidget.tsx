import { CourtGridBoardParams, useCourtGridBoard } from '../model';
import { MatchCard } from './components';
import {
	CourtCell,
	GridContainer,
	GridTable,
	LoadingWrapper,
	MatchCell,
	NoDataMessage,
	TimeHeaderCell,
	WidgetWrapper,
} from './styled';

type Props = CourtGridBoardParams & {
	gridStep?: number;
	focusedMatchId?: string | null;
	onMatchClick?: (matchId: string) => void;
};

export const CourtGridBoardWidget = (props: Props) => {
	const { focusedMatchId, onMatchClick } = props;
	const { courtsMatches, gridTimesPeriods, gridTimeStep, loading } = useCourtGridBoard(props);

	if (loading) {
		return (
			<WidgetWrapper>
				<LoadingWrapper>Loading court grid...</LoadingWrapper>
			</WidgetWrapper>
		);
	}

	if (!courtsMatches || courtsMatches.length === 0) {
		return (
			<WidgetWrapper>
				<NoDataMessage>No court data available for the selected time range</NoDataMessage>
			</WidgetWrapper>
		);
	}

	if (gridTimesPeriods.length === 0) {
		return (
			<WidgetWrapper>
				<NoDataMessage>No matches found for the selected time range</NoDataMessage>
			</WidgetWrapper>
		);
	}

	return (
		<WidgetWrapper>
			<GridContainer>
				<GridTable>
					<thead>
						<tr>
							<th>Court</th>
							{gridTimesPeriods.map(({ periodStart, periodDisplayName }) => (
								<TimeHeaderCell key={periodStart}>{periodDisplayName}</TimeHeaderCell>
							))}
						</tr>
					</thead>
					<tbody>
						{courtsMatches.map(({ court, matches, matchesDelaySeverity, tbMatchesCount }) => (
							<tr key={court.uuid}>
								<td>
									<CourtCell $delaySeverity={matchesDelaySeverity}>
										Ct {court.short_name || court.name}
									</CourtCell>
								</td>
								{gridTimesPeriods.map(({ periodStart, periodEnd }, index) => (
									<MatchCell
										key={`${court.uuid}_${periodStart}_${index}`}
										style={{
											height: tbMatchesCount ? tbMatchesCount * 96 : 120,
										}}
									>
										{matches.map((match) => {
											const { secs_start, secs_end } = match;
											// If match is not in current period or has no start/end time
											if (!(secs_start! >= periodStart && secs_start! < periodEnd && secs_end)) {
												return null;
											}

											return (
												<MatchCard
													key={match.match_id}
													match={match}
													focusedMatchId={focusedMatchId}
													minPeriod={gridTimeStep}
													onMatchClick={onMatchClick}
												/>
											);
										})}
									</MatchCell>
								))}
							</tr>
						))}
					</tbody>
				</GridTable>
			</GridContainer>
		</WidgetWrapper>
	);
};

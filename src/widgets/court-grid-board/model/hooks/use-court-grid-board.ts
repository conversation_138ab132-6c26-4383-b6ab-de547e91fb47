import { CourtGridBoardParams, CourtGridBoardReturnType } from '../types';
import { useCourts } from './use-courts';
import { useCourtsMatchesFilter } from './use-courts-matches-filter';
import { useCourtsMatchesSummary } from './use-courts-matches-summary';
import { useMatchesTimeGrid } from './use-matches-time-grid';
import { useRangeMatches } from './use-range-matches';

export const useCourtGridBoard = (params: CourtGridBoardParams): CourtGridBoardReturnType => {
	const { eswId, selectedCourtsIds } = params;

	const { allCourts, loading: courtsLoading } = useCourts(eswId);
	const { matches, hasMore, loading: matchesLoading } = useRangeMatches(params);
	// Merge all courts with related matches into a single structure
	const { courtsMatches } = useCourtsMatchesSummary({ allCourts, matches });

	const filteredCourtsMatches = useCourtsMatchesFilter({
		courtsMatches,
		courtsIds: selectedCourtsIds,
	});

	const [gridTimesPeriods, gridTimeStep] = useMatchesTimeGrid(filteredCourtsMatches);

	return {
		courtsMatches: filteredCourtsMatches || [],
		gridTimesPeriods,
		gridTimeStep,
		loading: courtsLoading || matchesLoading,
		hasMoreMatches: hasMore,
	};
};

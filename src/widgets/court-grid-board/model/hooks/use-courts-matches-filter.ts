import { useMemo } from 'react';

import { CourtsMatchesFilterParams, CourtsMatchesFilterReturnType } from '../types';

/**
 * Hook to filter courts matches based on provided court IDs.
 * Returns the initial list of courts matches if no IDs are provided, or a filtered list
 */
export const useCourtsMatchesFilter = ({
	courtsMatches,
	courtsIds,
}: CourtsMatchesFilterParams): CourtsMatchesFilterReturnType => {
	return useMemo(() => {
		if (!courtsMatches || !courtsIds) return courtsMatches;
		const courtsIdsSet = courtsIds instanceof Set ? courtsIds : new Set(courtsIds);
		return courtsIdsSet.size
			? courtsMatches.filter((courtMatch) => courtsIdsSet.has(courtMatch.court.uuid))
			: courtsMatches;
	}, [courtsMatches, courtsIds]);
};

import { useMemo } from 'react';

import { MatchDelaySeverity, getMatchDelaySeverity, maxDelaySeverity } from '@shared/domain/match';

import {
	Court,
	CourtMatchesGroup,
	CourtsMatchesSummaryParams,
	CourtsMatchesSummaryReturnType,
} from '../types';

/**
 * Hook for summarizing matches on courts
 * Summarize matches by court with delay severity and TB match count.
 * Returns a list of courts with their matches and aggregated data, skipping courts without matches.
 * @param allCourts
 * @param matches
 */
export const useCourtsMatchesSummary = ({
	allCourts,
	matches,
}: CourtsMatchesSummaryParams): CourtsMatchesSummaryReturnType => {
	return useMemo(() => {
		if (!allCourts || !matches) return { courts: null, courtsMatches: null };

		const courts: Court[] = [];
		const courtsMatches: CourtMatchesGroup[] = [];
		allCourts.forEach((court) => {
			const courtMatches = matches.filter((match) => match.court_id === court.uuid);
			if (!courtMatches.length) return;

			let matchesDelaySeverity = MatchDelaySeverity.None;
			let tbMatchesCount = 0;
			courtMatches.forEach((match) => {
				matchesDelaySeverity = maxDelaySeverity(getMatchDelaySeverity(match), matchesDelaySeverity);
				tbMatchesCount = match.is_tb ? tbMatchesCount + 1 : tbMatchesCount;
			});
			courtsMatches.push({
				court,
				matches: courtMatches,
				matchesDelaySeverity,
				tbMatchesCount,
			});

			courts.push(court);
		});

		return { courts, courtsMatches };
	}, [allCourts, matches]);
};

import { useMemo } from 'react';

import {
	DateTimeFormat,
	addHours,
	alignToStep,
	formatInUTC,
	getIntervalsInRange,
} from '@shared/lib/time';

import { CourtMatchesGroup, GridTimePeriod } from '../types';

const INITIAL_GRID_TIME_STEP = 30;

export const useMatchesTimeGrid = (courtsMatches: CourtMatchesGroup[] | null) => {
	return useMemo<[GridTimePeriod[], number]>(() => {
		if (!courtsMatches || courtsMatches.length === 0) return [[], INITIAL_GRID_TIME_STEP];

		let minStartTimestamp = Infinity;
		let maxStartTimestamp = -Infinity;
		let gridTimeStep = INITIAL_GRID_TIME_STEP;

		courtsMatches.forEach(({ matches }) => {
			matches.forEach(({ secs_start, secs_end }) => {
				if (secs_start && secs_end) {
					minStartTimestamp = Math.min(minStartTimestamp, secs_start);
					maxStartTimestamp = Math.max(maxStartTimestamp, secs_start);
					const startMinutes = new Date(secs_start).getUTCMinutes();
					// Detecting 15 minutes step
					gridTimeStep = gridTimeStep > 15 && [15, 45].includes(startMinutes) ? 15 : gridTimeStep;
				}
			});
		});

		if (minStartTimestamp === Infinity || maxStartTimestamp === -Infinity) {
			return [[], gridTimeStep];
		}

		// Choosing the end time based on the maximum start timestamp, taking into account that
		// the match can last more than an hour but should be displayed in an hour-ranged cell
		const alignedStartTime = alignToStep(new Date(minStartTimestamp), gridTimeStep);
		const alignedEndTime = alignToStep(addHours(new Date(maxStartTimestamp), 1), gridTimeStep);

		const gridTimesPeriods = getIntervalsInRange(
			alignedStartTime,
			alignedEndTime,
			gridTimeStep,
		).reduce<GridTimePeriod[]>((acc, date, i, allDates) => {
			const nextDate = allDates[i + 1];
			acc.push({
				periodStart: new Date(date).getTime(),
				periodEnd: nextDate ? new Date(nextDate).getTime() - 1 : Infinity, // -1 to avoid overlapping
				periodDisplayName: formatInUTC(new Date(date), DateTimeFormat.TwentyFourHour),
			});
			return acc;
		}, []);

		return [gridTimesPeriods, gridTimeStep];
	}, [courtsMatches]);
};

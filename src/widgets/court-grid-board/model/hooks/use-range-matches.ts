import { isValid } from 'date-fns';
import { useMemo } from 'react';

import { useRangeMatchesQuery } from '@entities/match';

import { RangeMatchesParams, RangeMatchesReturnType } from '../types';

export const useRangeMatches = ({
	eswId,
	divisionId,
	after,
	before,
}: RangeMatchesParams): RangeMatchesReturnType => {
	// Convert Date objects to ISO strings for GraphQL query
	const [afterISO, beforeISO] = useMemo<[string, string]>(() => {
		return [isValid(after) ? after.toISOString() : '', isValid(before) ? before.toISOString() : ''];
	}, [after, before]);

	const skip = !afterISO || !beforeISO || !eswId;
	const { data, loading } = useRangeMatchesQuery({
		variables: {
			eswId,
			after: afterISO,
			before: beforeISO,
			divisionId,
		},
		skip,
	});

	const dayRangeMatches = data?.dayRangeMatches;
	return !skip && dayRangeMatches
		? {
				matches: dayRangeMatches.items,
				hasMore: dayRangeMatches.filter_info.is_filtered,
				loading,
			}
		: {
				matches: null,
				hasMore: false,
				loading,
			};
};

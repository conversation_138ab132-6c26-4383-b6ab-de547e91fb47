import { useCourtsQuery } from '@entities/court';

import { buildCourtsMap } from '@shared/domain/court';

import { CourtsReturnType } from '../types';

export const useCourts = (eswId: string): CourtsReturnType => {
	const { data, loading } = useCourtsQuery({
		variables: {
			eswId,
		},
		skip: !eswId,
	});

	const allCourts = data?.courts ?? null;
	const allCourtsMap = allCourts ? buildCourtsMap(allCourts) : null;

	return {
		allCourts,
		allCourtsMap,
		loading,
	};
};

import { Court } from '@entities/court';
import { RangeMatch } from '@entities/match';

import { MatchDelaySeverity } from '@shared/domain/match';

export type { Court };

export type GridTimePeriod = {
	periodStart: number;
	periodEnd: number;
	periodDisplayName: string;
};

export type CourtsReturnType = {
	allCourts: Court[] | null;
	allCourtsMap: Map<string, Court> | null;
	loading: boolean;
};

export type RangeMatchesParams = {
	eswId: string;
	divisionId?: string | null;
	after: Date;
	before: Date;
};

export type RangeMatchesReturnType = {
	matches: RangeMatch[] | null;
	hasMore: boolean;
	loading: boolean;
};

// Combine Court and Match types to represent matches on courts and some additional metadata
export type CourtMatchesGroup = {
	court: Court;
	matches: RangeMatch[];
	matchesDelaySeverity: MatchDelaySeverity;
	tbMatchesCount: number;
};

export type CourtsMatchesSummaryParams = {
	allCourts: Court[] | null;
	matches: RangeMatch[] | null;
};

export type CourtsMatchesSummaryReturnType = {
	courts: Court[] | null;
	courtsMatches: CourtMatchesGroup[] | null;
};

export type CourtsMatchesFilterParams = {
	courtsMatches: CourtMatchesGroup[] | null;
	courtsIds?: string[] | Set<string>; // if provided, filter courts
};

export type CourtsMatchesFilterReturnType = CourtMatchesGroup[] | null;

export type CourtGridBoardParams = RangeMatchesParams & {
	selectedCourtsIds?: string[]; // if provided, filter courts
};

export type CourtGridBoardReturnType = {
	courtsMatches: CourtMatchesGroup[];
	gridTimesPeriods: GridTimePeriod[];
	gridTimeStep: number;
	loading: boolean;
	hasMoreMatches: boolean;
};

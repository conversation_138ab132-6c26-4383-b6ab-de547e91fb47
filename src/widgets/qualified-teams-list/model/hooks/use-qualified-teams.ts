import { capitalize } from 'lodash';
import { useMemo } from 'react';

import { useEventOverview } from '@entities/event-overview';
import { useQualifiedTeamsQuery } from '@entities/team';

import { EnrichedQualifiedTeam, QualifiedTeamsReturnType } from '../types';

export const useQualifiedTeams = (eswId: string): QualifiedTeamsReturnType => {
	const { divisions, loading: eventLoading } = useEventOverview(eswId);

	const { data, loading: teamsLoading } = useQualifiedTeamsQuery({
		variables: { eswId },
		skip: !eswId,
	});

	// Enrich qualified teams with division details and bid earned
	const qualifiedTeams = useMemo<EnrichedQualifiedTeam[] | null>(() => {
		if (!data?.qualifiedTeams || !divisions) return null;

		return divisions.reduce<EnrichedQualifiedTeam[]>((acc, division) => {
			const qualifiedTeams = data.qualifiedTeams.filter(
				(team) => team.division_id === division.division_id,
			);
			if (!qualifiedTeams.length) return acc;
			acc.push(
				...qualifiedTeams.map((team) => {
					const { prev_qual_age, prev_qual_division } = team.extra || {};
					const bid_earned = `${prev_qual_age || ''} ${capitalize(prev_qual_division || '')}`;
					return { ...team, division, bid_earned };
				}),
			);
			return acc;
		}, []);
	}, [data, divisions]);

	return {
		qualifiedTeams,
		loading: eventLoading || teamsLoading,
	};
};

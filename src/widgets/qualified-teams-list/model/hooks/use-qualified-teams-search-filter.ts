import { useMemo } from 'react';

import { EnrichedQualifiedTeam } from '../types';

export const useQualifiedTeamsSearchFilter = (
	teams?: EnrichedQualifiedTeam[] | null,
	search?: string | null,
): EnrichedQualifiedTeam[] | null => {
	return useMemo(() => {
		if (!teams) return null;
		if (!search) return teams;
		return teams.filter((team) => {
			return (
				team.team_name?.toLowerCase().includes(search) ||
				team.division.name.toLowerCase().includes(search) ||
				team.bid_earned?.toLowerCase().includes(search) ||
				team.extra?.earned_at?.toLowerCase().includes(search)
			);
		});
	}, [search, teams]);
};

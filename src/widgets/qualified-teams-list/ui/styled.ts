import styled from 'styled-components';

export const QualifiedTeamsWrapper = styled.div`
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 1rem;
`;

export const QualifiedTeamsTable = styled.table`
	width: 100%;
	border-collapse: collapse;
	margin-top: 1rem;

	thead tr td {
		padding: 0.75rem 0.5rem;
		font-weight: 700;
		background-color: #f4f6f8;

		&:first-child {
			padding-left: 1rem;
		}

		&:last-child {
			padding-right: 1rem;
		}
	}

	tbody tr {
		border-bottom: 1px solid #dfe3e8;

		td {
			padding: 0.75rem 0.5rem;

			&:first-child {
				padding-left: 1rem;
				font-weight: 700;
			}

			&:last-child {
				padding-right: 1rem;
			}

			&:nth-child(3) {
				font-weight: 700;
			}
		}
	}
`;

export const NoTeamsMessage = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	font-size: 1rem;
	color: #637381;
`;

export const LoadingWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
`;

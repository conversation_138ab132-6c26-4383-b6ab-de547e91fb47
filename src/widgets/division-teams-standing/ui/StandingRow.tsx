import { DivisionTeamWithStanding } from '../model';
import { RankB<PERSON>ge, StandingCell, StandingRow as StyledStandingRow, TeamName } from './styled';

export type Props = {
	team: DivisionTeamWithStanding;
	hasRanks: boolean;
};

export const StandingRow = ({ team, hasRanks }: Props) => {
	const getRankOrSeed = () => {
		if (hasRanks && team.division_standing?.rank) {
			return team.division_standing.rank;
		}
		if (!hasRanks && team.division_standing?.seed) {
			return team.division_standing.seed;
		}
		return null;
	};

	const getPoints = () => {
		return team.division_standing?.points || '-';
	};

	const rankOrSeed = getRankOrSeed();

	return (
		<StyledStandingRow>
			<StandingCell>{rankOrSeed ? <RankBadge>{rankOrSeed}</RankBadge> : '-'}</StandingCell>
			<StandingCell>
				<TeamName>{team.team_name}</TeamName>
			</StandingCell>
			<StandingCell>{getPoints()}</StandingCell>
		</StyledStandingRow>
	);
};

import styled from 'styled-components';

export const WidgetWrapper = styled.div`
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 1rem;
`;

export const StandingsTable = styled.table`
	width: 100%;
	border-collapse: collapse;
	background: #fff;
	border-radius: 4px;
	overflow: hidden;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

export const StandingsHeader = styled.thead`
	background-color: ${({ theme }) => theme.colors.primary || '#007bff'};
	color: white;
`;

export const HeaderRow = styled.tr`
	height: 48px;
`;

export const HeaderCell = styled.th`
	padding: 0.75rem;
	text-align: left;
	font-weight: 600;
	font-size: 0.875rem;

	&:first-child {
		width: 60px;
		text-align: center;
	}

	&:last-child {
		width: 100px;
		text-align: center;
	}
`;

export const StandingsBody = styled.tbody``;

export const StandingRow = styled.tr`
	border-bottom: 1px solid #e9ecef;

	&:hover {
		background-color: #f8f9fa;
	}

	&:last-child {
		border-bottom: none;
	}
`;

export const StandingCell = styled.td`
	padding: 0.75rem;
	font-size: 0.875rem;

	&:first-child {
		text-align: center;
		font-weight: 600;
	}

	&:last-child {
		text-align: center;
	}
`;

export const TeamName = styled.span`
	font-weight: 600;
	color: #333;
	cursor: pointer;

	&:hover {
		text-decoration: underline;
		color: ${({ theme }) => theme.colors.primary || '#007bff'};
	}
`;

export const RankBadge = styled.span`
	display: inline-flex;
	align-items: center;
	justify-content: center;
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background-color: ${({ theme }) => theme.colors.primary || '#007bff'};
	color: white;
	font-weight: 600;
	font-size: 0.875rem;
`;

export const LoadingWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	color: #666;
`;

export const NoStandingsMessage = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	color: #666;
	font-style: italic;
`;

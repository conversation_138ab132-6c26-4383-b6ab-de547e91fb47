import { useMemo } from 'react';

import { useDivisionTeamsStanding } from '../model';
import { StandingRow } from './StandingRow';
import {
	HeaderCell,
	HeaderRow,
	LoadingWrapper,
	NoStandingsMessage,
	StandingsBody,
	StandingsHeader,
	StandingsTable,
	WidgetWrapper,
} from './styled';

export type Props = {
	eswId: string;
	divisionId: string;
	search?: string | null;
};

export const DivisionTeamsStandingWidget = ({ eswId, divisionId, search }: Props) => {
	const { teams, loading, hasRanks } = useDivisionTeamsStanding({ eswId, divisionId });

	// Filter teams based on search if provided
	const filteredTeams = useMemo(() => {
		if (!teams || !search) return teams;

		const searchLower = search.toLowerCase();
		return teams.filter((team) => team.team_name.toLowerCase().includes(searchLower));
	}, [teams, search]);

	if (loading) {
		return (
			<LoadingWrapper>
				<div>Loading...</div>
			</LoadingWrapper>
		);
	}

	if (!filteredTeams || filteredTeams.length === 0) {
		return <NoStandingsMessage>No standings found</NoStandingsMessage>;
	}

	return (
		<WidgetWrapper>
			<StandingsTable>
				<StandingsHeader>
					<HeaderRow>
						<HeaderCell>{hasRanks ? 'Rank' : 'Seed'}</HeaderCell>
						<HeaderCell>Team</HeaderCell>
						<HeaderCell>Points</HeaderCell>
					</HeaderRow>
				</StandingsHeader>
				<StandingsBody>
					{filteredTeams.map((team) => (
						<StandingRow key={team.team_id} team={team} hasRanks={hasRanks} />
					))}
				</StandingsBody>
			</StandingsTable>
		</WidgetWrapper>
	);
};

import { useMemo } from 'react';

import { useDivisionTeamsStandingQuery } from '@entities/team';

import { DivisionTeamsStandingParams, DivisionTeamsStandingReturnType } from '../types';

export const useDivisionTeamsStanding = ({
	eswId,
	divisionId,
}: DivisionTeamsStandingParams): DivisionTeamsStandingReturnType => {
	// Use the GraphQL query hook
	const { data, loading } = useDivisionTeamsStandingQuery({
		variables: {
			eswId,
			divisionId,
		},
		skip: !eswId || !divisionId,
	});

	// Check if any team has a rank
	const hasRanks = useMemo(() => {
		if (!data?.divisionTeamsStanding || loading) return false;
		return data.divisionTeamsStanding.some((team) => !!team.division_standing?.rank);
	}, [data, loading]);

	return {
		teams: data?.divisionTeamsStanding ?? null,
		loading,
		hasRanks,
	};
};

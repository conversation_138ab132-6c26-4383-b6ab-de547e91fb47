import { useCallback, useRef } from 'react';

import { useFavoriteTeamsManager } from '@features/favorite-teams-manager';

import { PaginatedAthlete, usePaginatedAthletes } from '../model';
import { AthleteCard } from './AthleteCard';
import {
	AthleteI<PERSON>,
	AthletesList,
	LoadingWrapper,
	NoAthletesMessage,
	WidgetWrapper,
} from './styled';

export type Props = {
	eswId: string;
	search?: string | null;
};

export const AthletesListWidget = ({ eswId, search }: Props) => {
	const { data: athletes, loading, fetchNext } = usePaginatedAthletes({ eswId, search });
	const { favoriteTeamsIds } = useFavoriteTeamsManager(eswId);

	const listRef = useRef<HTMLUListElement>(null);

	// Handle scroll to load more athletes
	const handleScroll = useCallback(() => {
		if (!listRef.current) return;

		const { scrollTop, scrollHeight, clientHeight } = listRef.current;

		// If we're near the bottom, load more
		if (scrollTop + clientHeight >= scrollHeight - 100 && !loading) {
			fetchNext();
		}
	}, [fetchNext, loading]);

	// Add scroll event listener
	const handleScrollEvent = useCallback(() => {
		handleScroll();
	}, [handleScroll]);

	// Handle team modal opening (placeholder for now)
	const handleAthleteHeaderClick = useCallback((athlete: PaginatedAthlete) => {
		console.log('Open team modal for athlete team: ', athlete.team_id);
		// TODO: Implement modal functionality
	}, []);

	if (!athletes && loading) {
		return (
			<LoadingWrapper>
				<div>Loading...</div>
			</LoadingWrapper>
		);
	}

	if (!athletes || athletes.length === 0) {
		return <NoAthletesMessage>No athletes found</NoAthletesMessage>;
	}

	return (
		<WidgetWrapper>
			<AthletesList ref={listRef} onScroll={handleScrollEvent}>
				{athletes.map((athlete) => (
					<AthleteItem key={athlete.athlete_id}>
						<AthleteCard
							athlete={athlete}
							belongsToFavouriteTeam={favoriteTeamsIds.includes(athlete.team_id)} // TODO: Use hashmap for better performance
							onHeaderClick={handleAthleteHeaderClick}
						/>
					</AthleteItem>
				))}
				{loading && (
					<LoadingWrapper>
						<div>Loading more...</div>
					</LoadingWrapper>
				)}
			</AthletesList>
		</WidgetWrapper>
	);
};

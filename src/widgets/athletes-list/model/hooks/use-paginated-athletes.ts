import { useCallback, useEffect, useMemo, useState } from 'react';

import {
	PaginatedAthlete,
	PaginatedAthletesQuery,
	usePaginatedAthletesLazyQuery,
} from '@entities/athlete';

import { DEFAULT_PAGE_SIZE } from '@shared/config';

import { PaginatedAthletesParams, PaginatedAthletesReturnType } from '../types';

const INITIAL_PAGE = 1;

export const usePaginatedAthletes = (
	params: PaginatedAthletesParams,
): PaginatedAthletesReturnType => {
	const { eswId, search } = params;

	const [pagesResponses, setPagesResponses] = useState<PaginatedAthletesQuery[]>([]);

	const [fetchAthletes, { data, loading }] = usePaginatedAthletesLazyQuery();

	useEffect(() => {
		setPagesResponses([]);
		if (!eswId) return;

		fetchAthletes({
			variables: {
				eswId,
				page: INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		});
	}, [fetchAthletes, search, eswId]);

	// Update the pagesResponses array when a new page is fetched taking care of the order
	useEffect(() => {
		if (!data) return;

		setPagesResponses((prevResponses) => {
			const responses = [...prevResponses];
			responses[data.paginatedAthletes.page_info.page - 1] = data;
			return responses;
		});
	}, [data]);

	// Combine all athletes from all pages
	const athletes = useMemo(() => {
		return pagesResponses.reduce<PaginatedAthlete[]>((acc, response) => {
			if (!response) return acc;
			return [...acc, ...(response?.paginatedAthletes?.items ?? [])];
		}, []);
	}, [pagesResponses]);

	// Prepare the fetchNext function
	const fetchNext = useCallback(() => {
		let nextPage;
		const lastResponse = pagesResponses.at(-1);
		if (lastResponse) {
			const { page, page_count } = lastResponse.paginatedAthletes.page_info;
			if (page >= page_count) return;
			nextPage = page + 1;
		}
		fetchAthletes({
			variables: {
				eswId,
				page: nextPage || INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		});
	}, [pagesResponses, fetchAthletes, search, eswId]);

	return {
		data: athletes,
		loading,
		fetchNext,
	};
};

import { RoundDetails } from '@features/division-rounds-manager';

import { DivisionPoolBracket, DivisionPoolBracketsQueryVariables } from '@entities/pool-bracket';

import { RoundGroup, RoundGroupKeyType } from '@shared/domain/round';

export type DivisionPoolBracketParams = DivisionPoolBracketsQueryVariables;

export type DivisionPoolBracketsReturnType = {
	poolBrackets: DivisionPoolBracket[] | null;
	loading: boolean;
};

export type DivisionRoundPoolBracketsParams = {
	roundId: string;
	search?: string | null;
} & DivisionPoolBracketParams;

export type DivisionRoundPoolBracketsReturnType = {
	poolBrackets: DivisionPoolBracket[] | null;
	roundGroup: RoundGroup<RoundDetails> | null;
	roundGroupKey: RoundGroupKeyType | null;
	loading: boolean;
};

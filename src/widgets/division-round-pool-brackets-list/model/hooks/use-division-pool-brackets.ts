import { useDivisionPoolBracketsQuery } from '@entities/pool-bracket';

import { DivisionPoolBracketParams, DivisionPoolBracketsReturnType } from '../types';

export const useDivisionPoolBrackets = ({
	eswId,
	divisionId,
}: DivisionPoolBracketParams): DivisionPoolBracketsReturnType => {
	const { data, loading } = useDivisionPoolBracketsQuery({
		variables: {
			eswId,
			divisionId,
		},
		skip: !eswId || !divisionId,
	});

	return {
		poolBrackets: data?.divisionPoolBrackets ?? null,
		loading,
	};
};

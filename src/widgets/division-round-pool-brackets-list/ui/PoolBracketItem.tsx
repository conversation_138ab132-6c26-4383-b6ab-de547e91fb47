import { useMemo } from 'react';

import { type FavoriteTeamsManagerReturnType } from '@features/favorite-teams-manager';

import { DivisionPoolBracket } from '@entities/pool-bracket';

import bracketIcon from '@shared/assets/icons/bracket-icon.svg';
import poolIcon from '@shared/assets/icons/pool-icon.svg';
import { DateTimeFormat, formatInUTC } from '@shared/lib/time';

import { TeamItem } from './TeamItem';
import {
	EmptyMessage,
	PoolBracketHeader,
	PoolBracketIcon,
	PoolBracketItem as StyledPoolBracketItem,
	TeamsList,
} from './styled';

export type Props = {
	poolBracket: DivisionPoolBracket;
	divisionShortName?: string;
	onPoolBracketClick?: (poolBracketId: string) => void;
	onTeamClick?: (teamId: string) => void;
} & Pick<FavoriteTeamsManagerReturnType, 'favoriteTeamsIds' | 'toggleTeamFavorite'>;

export const PoolBracketItem = ({
	poolBracket,
	divisionShortName,
	favoriteTeamsIds,
	toggleTeamFavorite,
	onPoolBracketClick,
	onTeamClick,
}: Props) => {
	const poolBracketIcon = useMemo(() => {
		return poolBracket.is_pool ? poolIcon : bracketIcon;
	}, [poolBracket.is_pool]);

	const courtInfo = useMemo(() => {
		return (
			poolBracket.external?.courts_short_info?.map((court) => court.short_name).join(', ') || ''
		);
	}, [poolBracket.external]);

	const handlePoolBracketClick = () => {
		if (onPoolBracketClick) {
			onPoolBracketClick(poolBracket.uuid);
		}
	};

	const getMatchSettings = () => {
		if (!poolBracket.is_pool || !poolBracket.settings) return '';
		const { SetCount, PlayAllSets, WinningPoints } = poolBracket.settings;
		return `${SetCount ? `${SetCount} sets` : ''} ${PlayAllSets ? 'Play All' : ''} ${WinningPoints ? `to ${WinningPoints}` : ''}`.trim();
	};

	return (
		<StyledPoolBracketItem>
			<PoolBracketHeader onClick={handlePoolBracketClick}>
				<strong>
					<PoolBracketIcon src={poolBracketIcon} alt={poolBracket.is_pool ? 'Pool' : 'Bracket'} />
					{divisionShortName} {poolBracket.display_name}
				</strong>
				{poolBracket.date_start && (
					<span>Starts {formatInUTC(poolBracket.date_start, DateTimeFormat.WeekdayShortTime)}</span>
				)}
				{courtInfo && <span>Ct {courtInfo}</span>}
				{poolBracket.is_pool && <span>{getMatchSettings()}</span>}
			</PoolBracketHeader>

			<TeamsList>
				{!poolBracket.teams.length && <EmptyMessage>Teams are not assigned yet</EmptyMessage>}
				{poolBracket.teams.map((team) => (
					<TeamItem
						key={team.team_id}
						team={team}
						favoriteTeamsIds={favoriteTeamsIds}
						toggleTeamFavorite={toggleTeamFavorite}
						onTeamClick={onTeamClick}
					/>
				))}
			</TeamsList>
		</StyledPoolBracketItem>
	);
};

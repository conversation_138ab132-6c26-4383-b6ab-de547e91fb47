import styled from 'styled-components';

export const WidgetWrapper = styled.div`
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 1rem;
`;

export const RoundTitle = styled.h2`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
	font-size: 1.5rem;
`;

export const PoolBracketsList = styled.ul`
	list-style: none;
	padding: 0;
	margin: 0;
	max-height: 70vh;
	overflow-y: auto;
`;

export const PoolBracketItem = styled.li`
	margin: 0 0 1rem;
	border: 1px solid #f4f6f8;
	border-radius: 4px;
	background: #fff;
`;

export const PoolBracketHeader = styled.div`
	padding: 1rem;
	border-bottom: 1px solid #f4f6f8;
	background: #fff;
	cursor: pointer;

	&:hover {
		strong {
			text-decoration: underline;
		}
	}

	strong {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		font-size: 1rem;
		color: ${({ theme }) => theme.colors.text};
	}

	span {
		font-size: 0.875rem;
		color: ${({ theme }) => theme.colors.secondary};
		margin-left: 0.5rem;
	}
`;

export const PoolBracketIcon = styled.img`
	width: 16px;
	height: 16px;
`;

export const TeamsList = styled.ul`
	list-style: none;
	padding: 0;
	margin: 0;
`;

export const TeamItem = styled.li<{ $isShowStat: boolean }>`
	display: flex;
	align-items: center;
	background: #f4f6f8;
	padding: 1rem;
	margin: 0 0 0.5rem;
	cursor: pointer;

	&:hover {
		div:first-child {
			text-decoration: underline;
		}
	}

	&:last-child {
		margin-bottom: 0;
	}
`;

export const TeamInfo = styled.div`
	flex: 1;
	display: flex;
	align-items: center;
	gap: 0.5rem;
	position: relative;
`;

export const TeamName = styled.span<{ $extraOffset?: boolean }>`
	display: inline-block;
	width: ${({ $extraOffset }) => ($extraOffset ? '70%' : '88%')};
	text-align: left;
	font-weight: 500;
	color: ${({ theme }) => theme.colors.text};
`;

export const FavoriteIcon = styled.button`
	background: none;
	border: none;
	cursor: pointer;
	padding: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 24px;
	height: 24px;

	img {
		width: 16px;
		height: 16px;
	}
`;

export const TeamStats = styled.div`
	display: flex;
	gap: 1rem;
	align-items: center;

	div {
		padding: 0 0.25rem;
		font-weight: 400;
		line-height: 18px;
		text-align: right;
		font-size: 14px;

		span {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			font-weight: 400;
			line-height: 18px;
			text-align: right;
			font-size: 14px;
		}

		i {
			font-style: normal;
			font-weight: 700;
		}
	}
`;

export const EmptyMessage = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	font-size: 1rem;
	color: #637381;
	font-style: italic;
`;

export const LoadingWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
`;

export const NoPoolBracketsMessage = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	font-size: 1rem;
	color: #637381;
`;

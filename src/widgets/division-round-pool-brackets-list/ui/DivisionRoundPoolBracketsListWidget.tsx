import { useMemo } from 'react';

import { useFavoriteTeamsManager } from '@features/favorite-teams-manager';

import { useDivisionRoundPoolBrackets } from '../model';
import { PoolBracketItem } from './PoolBracketItem';
import {
	LoadingWrapper,
	NoPoolBracketsMessage,
	PoolBracketsList,
	RoundTitle,
	WidgetWrapper,
} from './styled';

export type Props = {
	eswId: string;
	divisionId: string;
	roundId: string;
	search?: string | null;
};

export const DivisionRoundPoolBracketsListWidget = ({
	eswId,
	divisionId,
	roundId,
	search,
}: Props) => {
	const { poolBrackets, roundGroup, loading } = useDivisionRoundPoolBrackets({
		eswId,
		divisionId,
		roundId,
	});

	const { favoriteTeamsIds, toggleTeamFavorite } = useFavoriteTeamsManager(eswId);

	// Filter pool brackets based on search if provided
	const filteredPoolBrackets = useMemo(() => {
		if (!poolBrackets || !search) return poolBrackets;

		const searchLower = search.toLowerCase();
		return poolBrackets
			.map((poolBracket) => ({
				...poolBracket,
				teams: poolBracket.teams.filter((team) =>
					team.team_name.toLowerCase().includes(searchLower),
				),
			}))
			.filter((poolBracket) => poolBracket.teams.length > 0);
	}, [poolBrackets, search]);

	// Placeholder handlers for future implementation
	const handlePoolBracketClick = (poolBracketId: string) => {
		console.log('Pool bracket clicked:', poolBracketId);
		// TODO: Implement pool bracket modal or navigation
	};

	const handleTeamClick = (teamId: string) => {
		console.log('Team clicked:', teamId);
		// TODO: Implement team modal
	};

	if (loading) {
		return (
			<WidgetWrapper>
				<LoadingWrapper>
					<div>Loading...</div>
				</LoadingWrapper>
			</WidgetWrapper>
		);
	}

	if (!filteredPoolBrackets || filteredPoolBrackets.length === 0) {
		return (
			<WidgetWrapper>
				{roundGroup && <RoundTitle>{roundGroup.name}</RoundTitle>}
				<NoPoolBracketsMessage>
					{search
						? 'No pool brackets found matching your search'
						: 'No pool brackets found for this round'}
				</NoPoolBracketsMessage>
			</WidgetWrapper>
		);
	}

	return (
		<WidgetWrapper>
			{roundGroup && <RoundTitle>{roundGroup.name}</RoundTitle>}
			<PoolBracketsList>
				{filteredPoolBrackets.map((poolBracket) => (
					<PoolBracketItem
						key={poolBracket.uuid}
						poolBracket={poolBracket}
						favoriteTeamsIds={favoriteTeamsIds}
						toggleTeamFavorite={toggleTeamFavorite}
						onPoolBracketClick={handlePoolBracketClick}
						onTeamClick={handleTeamClick}
					/>
				))}
			</PoolBracketsList>
		</WidgetWrapper>
	);
};

import { useMemo } from 'react';

import {
	FavoriteStatus,
	FavoriteStatusIcon,
	type FavoriteTeamsManagerReturnType,
} from '@features/favorite-teams-manager';

import { DateTimeFormat, formatInUTC } from '@shared/lib/time';

import { PaginatedClubTeam } from '../model';
import { DivisionName, TeamDetails, TeamInfo, TeamStatus } from './styled';

export type Props = {
	team: PaginatedClubTeam;
	openTeamModal?: (team: PaginatedClubTeam) => void;
} & Pick<FavoriteTeamsManagerReturnType, 'favoriteTeamsIds' | 'toggleTeamFavorite'>;

export const TeamItem = ({ team, favoriteTeamsIds, toggleTeamFavorite, openTeamModal }: Props) => {
	const hasMatch = !!team.next_match;
	const isFavorite = useMemo(
		() => favoriteTeamsIds.includes(team.team_id),
		[favoriteTeamsIds, team.team_id],
	);

	const onTeamClick = () => {
		if (openTeamModal) {
			openTeamModal(team);
		}
	};

	const onToggleTeamFavorite = (e: React.MouseEvent) => {
		e.stopPropagation();
		toggleTeamFavorite(team);
	};

	return (
		<div>
			<TeamInfo $isPlayed={!hasMatch} onClick={onTeamClick}>
				<DivisionName>{team.division_name}</DivisionName>
				<TeamDetails>
					<div>
						<FavoriteStatusIcon
							status={isFavorite ? FavoriteStatus.Full : FavoriteStatus.None}
							onClick={onToggleTeamFavorite}
						/>
						<strong>{team.team_name}</strong>
					</div>
					{hasMatch && <div>vs {team.next_match?.external.opponent_display_name}</div>}
				</TeamDetails>
			</TeamInfo>

			<TeamStatus $isPlayed={!hasMatch}>
				{!hasMatch ? (
					<p>
						<strong>
							{team.division_standing && (
								<>
									{team.division_standing.matches_won}-{team.division_standing.matches_lost}
								</>
							)}
						</strong>
					</p>
				) : (
					<div>
						<p>{formatInUTC(team.next_match?.secs_start || 0, DateTimeFormat.WeekdayShortTime)}</p>
						<p>{team.next_match?.external.court_info?.short_name || 'TBD'}</p>
					</div>
				)}
			</TeamStatus>
		</div>
	);
};

import styled from 'styled-components';

export const WidgetWrapper = styled.div`
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 1rem;
`;

export const ClubsList = styled.ul`
	list-style: none;
	padding: 0;
	margin: 0;
	max-height: 70vh;
	overflow-y: auto;
`;

export const ClubItem = styled.li`
	border-radius: 4px;
	border: 1px solid #dfe3e8;
	margin: 0 0 1rem;
	position: relative;
`;

export const ClubHeader = styled.div`
	padding: 1rem;
	font-weight: 700;
	display: flex;
	align-items: center;
	gap: 0.5rem;
`;

export const ClubFooter = styled.div<{ $isOpen?: boolean }>`
	display: flex;
	justify-content: space-between;
	padding: 0 1rem 0.75rem 1rem;
	border-bottom: ${(props) => (props.$isOpen ? '1px solid #dfe3e8' : 'none')};
`;

export const ClubCode = styled.div`
	font-size: 0.875rem;
`;

export const TeamsCount = styled.div`
	font-size: 0.875rem;
	display: flex;
	align-items: center;
	cursor: pointer;

	img {
		margin-left: 0.5rem;
	}
`;

export const TeamsList = styled.ul`
	list-style: none;
	padding: 0.625rem 0.25rem 0.625rem 1.5rem;
	margin: 0;
`;

export const TeamItem = styled.li`
	display: flex;
	justify-content: space-between;
	margin-bottom: 0.5rem;
	gap: 0.5rem;

	@media (min-width: ${({ theme }) => theme.breakpoints.small}) {
		margin-bottom: 1rem;
		gap: 1rem;
	}
`;

export const TeamInfo = styled.div<{ $isPlayed?: boolean }>`
	border-radius: 4px;
	border: 1px solid #dfe3e8;
	padding: 1rem;
	width: 70%;
	display: flex;
	gap: 1rem;
	background: ${(props) => (props.$isPlayed ? '#F4F6F8' : 'transparent')};
	cursor: pointer;

	&:hover {
		background: ${(props) => (props.$isPlayed ? '#edeff1' : '#f9f9f9')};
	}
`;

export const DivisionName = styled.div`
	font-weight: 700;
	width: 4rem;
`;

export const TeamDetails = styled.div`
	position: relative;
	width: 100%;

	strong {
		font-weight: 700;
	}
`;

export const TeamStatus = styled.div<{ $isPlayed?: boolean }>`
	border-radius: 4px;
	border: 1px solid #dfe3e8;
	padding: 0.5rem;
	width: 30%;
	min-width: 7.5rem;
	display: flex;
	align-items: center;
	justify-content: center;
	background: ${(props) => (props.$isPlayed ? '#F4F6F8' : 'transparent')};
	text-align: center;
	cursor: pointer;

	&:hover {
		background: ${(props) => (props.$isPlayed ? '#edeff1' : '#f9f9f9')};
	}
`;

export const LoadingWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
`;

export const NoClubsMessage = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	font-size: 1rem;
	color: #637381;
`;

export const TeamWrapper = styled.div``;

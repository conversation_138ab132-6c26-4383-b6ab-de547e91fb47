import { useMemo, useState } from 'react';

import {
	FavoriteStatusIcon,
	type FavoriteTeamsManagerReturnType,
	getGroupFavoriteStatus,
} from '@features/favorite-teams-manager';

import expandMoreIcon from '@shared/assets/icons/chevron-down-icon.svg';
import expandLessIcon from '@shared/assets/icons/chevron-up-icon.svg';

import { PaginatedClub } from '../model';
import { TeamItem } from './TeamItem';
import {
	ClubCode,
	ClubFooter,
	ClubHeader,
	TeamItem as StyledTeamItem,
	TeamWrapper,
	TeamsCount,
	TeamsList,
} from './styled';

export type Props = {
	club: PaginatedClub;
} & FavoriteTeamsManagerReturnType;

export const ClubItem = ({
	club,
	favoriteTeamsIds,
	toggleTeamFavorite,
	toggleClubTeamsFavorite,
}: Props) => {
	const { teams } = club;
	const [isShowTeams, setIsShowTeams] = useState(false);

	const favoriteStatus = useMemo(
		() => getGroupFavoriteStatus(teams, favoriteTeamsIds),
		[teams, favoriteTeamsIds],
	);

	const onToggleClubTeamsFavorite = (e: React.MouseEvent) => {
		e.stopPropagation();
		toggleClubTeamsFavorite(club);
	};

	return (
		<>
			<ClubHeader>
				<FavoriteStatusIcon status={favoriteStatus} onClick={onToggleClubTeamsFavorite} />
				{club.club_name}, {club.state}
			</ClubHeader>

			<ClubFooter $isOpen={isShowTeams}>
				<ClubCode>{club.club_code}</ClubCode>
				<TeamsCount onClick={() => setIsShowTeams(!isShowTeams)}>
					{club.teams_count} Teams
					<img
						src={isShowTeams ? expandLessIcon : expandMoreIcon}
						alt={isShowTeams ? 'Hide teams' : 'Show teams'}
					/>
				</TeamsCount>
			</ClubFooter>

			{isShowTeams && (
				<TeamWrapper>
					<TeamsList>
						{club.teams.map((team) => (
							<StyledTeamItem key={team.team_id}>
								<TeamItem
									team={team}
									favoriteTeamsIds={favoriteTeamsIds}
									toggleTeamFavorite={toggleTeamFavorite}
								/>
							</StyledTeamItem>
						))}
					</TeamsList>
				</TeamWrapper>
			)}
		</>
	);
};

import { useCallback, useEffect, useRef } from 'react';

import { useFavoriteTeamsManager } from '@features/favorite-teams-manager';

import { usePaginatedClubs } from '../model';
import { ClubItem } from './ClubItem';
import {
	ClubsList,
	LoadingWrapper,
	NoClubsMessage,
	ClubItem as StyledClubItem,
	WidgetWrapper,
} from './styled';

export type Props = {
	eswId: string;
	search?: string | null;
};

export const ClubsTeamsListWidget = ({ eswId, search }: Props) => {
	const { data: clubs, loading, fetchNext } = usePaginatedClubs({ eswId, search });
	const { favoriteTeamsIds, toggleTeamFavorite, toggleClubTeamsFavorite } =
		useFavoriteTeamsManager(eswId);

	const listRef = useRef<HTMLUListElement>(null);

	// Handle scroll to load more clubs
	const handleScroll = useCallback(() => {
		if (!listRef.current) return;

		const { scrollTop, scrollHeight, clientHeight } = listRef.current;

		// If we're near the bottom, load more
		if (scrollTop + clientHeight >= scrollHeight - 100 && !loading) {
			fetchNext();
		}
	}, [fetchNext, loading]);

	// Add scroll event listener
	useEffect(() => {
		const listElement = listRef.current;
		if (listElement) {
			listElement.addEventListener('scroll', handleScroll);
			return () => {
				listElement.removeEventListener('scroll', handleScroll);
			};
		}
	}, [handleScroll]);

	if (loading && (!clubs || clubs.length === 0)) {
		return (
			<LoadingWrapper>
				<div>Loading...</div>
			</LoadingWrapper>
		);
	}

	if (!clubs || clubs.length === 0) {
		return <NoClubsMessage>No clubs found</NoClubsMessage>;
	}

	return (
		<WidgetWrapper>
			<ClubsList ref={listRef}>
				{clubs.map((club) => (
					<StyledClubItem key={club.roster_club_id}>
						<ClubItem
							club={club}
							favoriteTeamsIds={favoriteTeamsIds}
							toggleTeamFavorite={toggleTeamFavorite}
							toggleClubTeamsFavorite={toggleClubTeamsFavorite}
						/>
					</StyledClubItem>
				))}
				{loading && (
					<LoadingWrapper>
						<div>Loading more...</div>
					</LoadingWrapper>
				)}
			</ClubsList>
		</WidgetWrapper>
	);
};

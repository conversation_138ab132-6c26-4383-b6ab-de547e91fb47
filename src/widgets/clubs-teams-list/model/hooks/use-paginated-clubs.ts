import { useCallback, useEffect, useMemo, useState } from 'react';

import { PaginatedClub, PaginatedClubsQuery, usePaginatedClubsLazyQuery } from '@entities/club';

import { DEFAULT_PAGE_SIZE } from '@shared/config';

import { PaginatedClubsParams, PaginatedClubsReturnType } from '../types';

const INITIAL_PAGE = 1;

export const usePaginatedClubs = (params: PaginatedClubsParams): PaginatedClubsReturnType => {
	const { eswId, search } = params;
	const [pagesResponses, setPagesResponses] = useState<PaginatedClubsQuery[]>([]);

	const [fetchClubs, { data, loading }] = usePaginatedClubsLazyQuery();

	useEffect(() => {
		setPagesResponses([]);
		if (!eswId) return;

		fetchClubs({
			variables: {
				eswId,
				page: INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		});
	}, [fetchClubs, search, eswId]);

	// Update the pagesResponses array when a new page is fetched taking care of the order
	useEffect(() => {
		if (!data) return;

		setPagesResponses((prevResponses) => {
			const responses = [...prevResponses];
			responses[data.paginatedClubs.page_info.page - 1] = data;
			return responses;
		});
	}, [data]);

	// Combine all clubs from all pages
	const clubs = useMemo(() => {
		return pagesResponses.reduce<PaginatedClub[]>((acc, response) => {
			if (!response) return acc;
			return [...acc, ...(response?.paginatedClubs?.items ?? [])];
		}, []);
	}, [pagesResponses]);

	// Prepare the fetchNext function
	const fetchNext = useCallback(() => {
		let nextPage;
		const lastResponse = pagesResponses.at(-1);
		if (lastResponse) {
			const { page, page_count } = lastResponse.paginatedClubs.page_info;
			if (page >= page_count) return;
			nextPage = page + 1;
		}
		fetchClubs({
			variables: {
				eswId,
				page: nextPage || INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		});
	}, [pagesResponses, fetchClubs, search, eswId]);

	return {
		data: clubs,
		loading,
		fetchNext,
	};
};

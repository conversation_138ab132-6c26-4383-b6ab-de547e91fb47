import { DivisionDetails } from '@entities/event-overview';

import { MatchesTimeRangeRef } from '@shared/domain/event-overview';

export type CourtGridControlsProps = {
	divisions: DivisionDetails[];
	selectedDivisionId: string;
	selectedDay: string;
	availableDays: string[];
	selectedStartTime: string;
	selectedEndTime: string;
	timeRange: MatchesTimeRangeRef;
	selectedGridStep: number;
	onDivisionChange: (divisionId: string) => void;
	onDayChange: (day: string) => void;
	onStartTimeChange: (time: string) => void;
	onEndTimeChange: (time: string) => void;
	onGridStepChange: (step: number) => void;
};

import { useMemo } from 'react';

import { DivisionsListOptionsParams, buildDivisionsListOptions } from '@shared/domain/division';
import { DateTimeFormat, formatInUTC } from '@shared/lib/time';
import { SelectOption } from '@shared/model';

import { CourtGridControlsProps } from '../model';
import { ControlGroup, ControlsWrapper, Input, Label, Select } from './styled';

const DIVISIONS_LIST_OPTIONS_PARAMS: DivisionsListOptionsParams = {
	withAllDivisionOption: true,
	shortName: true,
};

const GRID_STEP_OPTIONS = [
	{ id: 15, label: '15 minutes' },
	{ id: 30, label: '30 minutes' },
	{ id: 60, label: '60 minutes' },
];

// Helper function to build days list options
const buildDaysListOptions = (days: string[]): SelectOption[] => {
	return days.map((day) => ({
		id: day,
		label: formatInUTC(day, DateTimeFormat.WeekdayShortMonthDay),
	}));
};

export const CourtGridControlsWidget = ({
	divisions,
	selectedDivisionId,
	selectedDay,
	availableDays,
	selectedStartTime,
	selectedEndTime,
	timeRange,
	selectedGridStep,
	onDivisionChange,
	onDayChange,
	onStartTimeChange,
	onEndTimeChange,
	onGridStepChange,
}: CourtGridControlsProps) => {
	const divisionsOptions = useMemo(
		() => buildDivisionsListOptions(divisions, DIVISIONS_LIST_OPTIONS_PARAMS),
		[divisions],
	);

	const daysOptions = useMemo(() => buildDaysListOptions(availableDays), [availableDays]);

	// Calculate min/max for time inputs based on timeRange and current selections
	const startTimeMin = timeRange.start_time.substring(0, 5);
	const startTimeMax = selectedEndTime || timeRange.end_time.substring(0, 5);
	const endTimeMin = selectedStartTime || timeRange.start_time.substring(0, 5);
	const endTimeMax = timeRange.end_time.substring(0, 5);

	return (
		<ControlsWrapper>
			<ControlGroup>
				<Label>Division</Label>
				<Select value={selectedDivisionId} onChange={(e) => onDivisionChange(e.target.value)}>
					{divisionsOptions.map((option) => (
						<option key={option.id} value={option.id}>
							{option.label}
						</option>
					))}
				</Select>
			</ControlGroup>

			<ControlGroup>
				<Label>Day</Label>
				<Select value={selectedDay} onChange={(e) => onDayChange(e.target.value)}>
					{daysOptions.map((option) => (
						<option key={option.id} value={option.id}>
							{option.label}
						</option>
					))}
				</Select>
			</ControlGroup>

			<ControlGroup>
				<Label>Start Time</Label>
				<Input
					type="time"
					value={selectedStartTime}
					min={startTimeMin}
					max={startTimeMax}
					onChange={(e) => onStartTimeChange(e.target.value)}
				/>
			</ControlGroup>

			<ControlGroup>
				<Label>End Time</Label>
				<Input
					type="time"
					value={selectedEndTime}
					min={endTimeMin}
					max={endTimeMax}
					onChange={(e) => onEndTimeChange(e.target.value)}
				/>
			</ControlGroup>

			<ControlGroup>
				<Label>Grid Step (minutes)</Label>
				<Select value={selectedGridStep} onChange={(e) => onGridStepChange(Number(e.target.value))}>
					{GRID_STEP_OPTIONS.map((option) => (
						<option key={option.id} value={option.id}>
							{option.label}
						</option>
					))}
				</Select>
			</ControlGroup>
		</ControlsWrapper>
	);
};

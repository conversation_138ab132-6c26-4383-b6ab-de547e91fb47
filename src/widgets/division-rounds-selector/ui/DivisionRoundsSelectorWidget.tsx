import { RoundDetails } from '@features/division-rounds-manager';

import { useDivisionRoundsSelector } from '../model';
import { RoundGroupItem } from './RoundGroupItem';
import {
	LoadingWrapper,
	NoRoundsMessage,
	RoundGroupsList,
	WidgetTitle,
	WidgetWrapper,
} from './styled';

export type Props = {
	eswId: string;
	divisionId: string;
	roundId: string;
	onRoundSelect: (round: RoundDetails) => void;
};

export const DivisionRoundsSelectorWidget = ({
	eswId,
	divisionId,
	roundId,
	onRoundSelect,
}: Props) => {
	const { roundsGroups, selectedRoundGroup, loading, handleRoundGroupSelect } =
		useDivisionRoundsSelector({
			eswId,
			divisionId,
			roundId,
			onRoundSelect,
		});

	if (loading) {
		return (
			<WidgetWrapper>
				<WidgetTitle>Select Round</WidgetTitle>
				<LoadingWrapper>
					<div>Loading rounds...</div>
				</LoadingWrapper>
			</WidgetWrapper>
		);
	}

	if (!roundsGroups || roundsGroups.length === 0) {
		return (
			<WidgetWrapper>
				<WidgetTitle>Select Round</WidgetTitle>
				<NoRoundsMessage>No rounds available for this division</NoRoundsMessage>
			</WidgetWrapper>
		);
	}

	return (
		<WidgetWrapper>
			<WidgetTitle>Select Round</WidgetTitle>
			<RoundGroupsList>
				{roundsGroups.map((roundGroup) => {
					const isActive = roundGroup === selectedRoundGroup;
					return (
						<RoundGroupItem
							key={roundGroup.groupKey}
							roundGroup={roundGroup}
							isActive={isActive}
							onSelect={handleRoundGroupSelect}
						/>
					);
				})}
			</RoundGroupsList>
		</WidgetWrapper>
	);
};

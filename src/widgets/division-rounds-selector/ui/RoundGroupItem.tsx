import { useMemo } from 'react';

import { RoundDetails } from '@features/division-rounds-manager';

import { RoundGroup } from '@shared/domain/round';
import { DateTimeFormat, formatInUTC } from '@shared/lib/time';

import {
	RoundGroupDuration,
	RoundGroupName,
	RoundGroupItem as StyledRoundGroupItem,
} from './styled';

export type Props = {
	roundGroup: RoundGroup<RoundDetails>;
	isActive: boolean;
	onSelect: (roundGroup: RoundGroup<RoundDetails>) => void;
};

export const RoundGroupItem = ({ roundGroup, isActive, onSelect }: Props) => {
	// Format the duration for display
	const formattedDuration = useMemo(() => {
		const { first_match_start, last_match_start } = roundGroup;

		if (!first_match_start) return 'No schedule available';

		try {
			const startFormatted = formatInUTC(first_match_start, DateTimeFormat.MonthShortDay);
			const endFormatted = last_match_start
				? formatInUTC(last_match_start, DateTimeFormat.MonthShortDay)
				: startFormatted;

			// If same day, show just one date
			if (startFormatted === endFormatted) {
				return startFormatted;
			}

			return `${startFormatted} - ${endFormatted}`;
		} catch {
			return 'Invalid date';
		}
	}, [roundGroup]);

	const handleClick = () => {
		onSelect(roundGroup);
	};

	return (
		<StyledRoundGroupItem $isActive={isActive} onClick={handleClick}>
			<RoundGroupName>{roundGroup.name || roundGroup.short_name || 'Unnamed Round'}</RoundGroupName>
			<RoundGroupDuration>{formattedDuration}</RoundGroupDuration>
		</StyledRoundGroupItem>
	);
};

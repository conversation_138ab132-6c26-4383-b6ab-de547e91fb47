import { useCallback, useMemo } from 'react';

import { RoundDetails, useDivisionRoundsManager } from '@features/division-rounds-manager';

import { RoundGroup } from '@shared/domain/round';

import { DivisionRoundsSelectorParams, DivisionRoundsSelectorReturnType } from '../types';

export const useDivisionRoundsSelector = ({
	eswId,
	divisionId,
	roundId,
	onRoundSelect,
}: DivisionRoundsSelectorParams): DivisionRoundsSelectorReturnType => {
	const {
		roundsGroups,
		getRoundGroupKeyByRoundId,
		getRoundGroupByGroupKey,
		getRoundsByGroupKey,
		loading,
	} = useDivisionRoundsManager({ eswId, divisionId });

	// Determine the currently selected round group based on the provided roundId
	const selectedRoundGroup = useMemo<RoundGroup<RoundDetails> | null>(() => {
		if (!roundsGroups) return null;
		const roundGroupKey = getRoundGroupKeyByRoundId(roundId);
		return roundGroupKey ? getRoundGroupByGroupKey(roundGroupKey) : null;
	}, [roundsGroups, roundId, getRoundGroupKeyByRoundId, getRoundGroupByGroupKey]);

	// Handle round group selection
	const handleRoundGroupSelect = useCallback(
		(roundGroup: RoundGroup<RoundDetails>) => {
			const [firstRound] = getRoundsByGroupKey(roundGroup.groupKey) || [];
			if (!firstRound) return;
			onRoundSelect(firstRound);
		},
		[onRoundSelect, getRoundsByGroupKey],
	);

	return {
		roundsGroups,
		selectedRoundGroup,
		handleRoundGroupSelect,
		loading,
	};
};

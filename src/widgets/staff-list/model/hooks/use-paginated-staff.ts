import { useCallback, useEffect, useMemo, useState } from 'react';

import {
	PaginatedStaff,
	PaginatedStaffQuery,
	usePaginatedStaffLazyQuery,
} from '@entities/staff-member';

import { DEFAULT_PAGE_SIZE } from '@shared/config';

import { PaginatedStaffParams, PaginatedStaffReturnType } from '../types';

const INITIAL_PAGE = 1;

export const usePaginatedStaff = (params: PaginatedStaffParams): PaginatedStaffReturnType => {
	const { eswId, search } = params;

	const [pagesResponses, setPagesResponses] = useState<PaginatedStaffQuery[]>([]);

	const [fetchStaff, { data, loading }] = usePaginatedStaffLazyQuery();

	useEffect(() => {
		setPagesResponses([]);
		if (!eswId) return;

		fetchStaff({
			variables: {
				eswId,
				page: INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		});
	}, [fetchStaff, search, eswId]);

	// Update the pagesResponses array when a new page is fetched taking care of the order
	useEffect(() => {
		if (!data) return;

		setPagesResponses((prevResponses) => {
			const responses = [...prevResponses];
			responses[data.paginatedStaff.page_info.page - 1] = data;
			return responses;
		});
	}, [data]);

	// Combine all staff-member from all pages
	const staff = useMemo(() => {
		return pagesResponses.reduce<PaginatedStaff[]>((acc, response) => {
			if (!response) return acc;
			return [...acc, ...(response?.paginatedStaff?.items ?? [])];
		}, []);
	}, [pagesResponses]);

	// Prepare the fetchNext function
	const fetchNext = useCallback(() => {
		let nextPage;
		const lastResponse = pagesResponses.at(-1);
		if (lastResponse) {
			const { page, page_count } = lastResponse.paginatedStaff.page_info;
			if (page >= page_count) return;
			nextPage = page + 1;
		}
		fetchStaff({
			variables: {
				eswId,
				page: nextPage || INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		});
	}, [pagesResponses, fetchStaff, search, eswId]);

	return {
		data: staff,
		loading,
		fetchNext,
	};
};

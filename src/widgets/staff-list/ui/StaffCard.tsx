import { FavoriteStatus, FavoriteStatusIcon } from '@features/favorite-teams-manager';

import { PaginatedStaff } from '../model';
import {
	RosterCard,
	RosterCardContent,
	RosterCardContentWrapper,
	R<PERSON><PERSON><PERSON><PERSON><PERSON>ooter,
	RosterCardHeader,
} from './styled';

export type Props = {
	staff: PaginatedStaff;
	belongsToFavouriteTeam: boolean;
	onHeaderClick: (staff: PaginatedStaff) => void;
};

export const StaffCard = ({ staff, belongsToFavouriteTeam, onHeaderClick }: Props) => {
	const role = staff.role_name ? ` - ${staff.role_name}` : '';
	const header = `${staff.first} ${staff.last}${role}`;
	const content = `${staff.team_name} - ${staff.organization_code}`;
	const footer = `${staff.club_name}, ${staff.state}`;

	const handleHeaderClick = (e: React.MouseEvent) => {
		e.stopPropagation();
		onHeaderClick(staff);
	};

	return (
		<RosterCard>
			<RosterCardHeader className="roster-card__header" onClick={handleHeaderClick}>
				<FavoriteStatusIcon
					status={belongsToFavouriteTeam ? FavoriteStatus.Full : FavoriteStatus.None}
				/>
				{header}
			</RosterCardHeader>
			<RosterCardContentWrapper>
				<RosterCardContent>{content}</RosterCardContent>
				<RosterCardFooter>{footer}</RosterCardFooter>
			</RosterCardContentWrapper>
		</RosterCard>
	);
};

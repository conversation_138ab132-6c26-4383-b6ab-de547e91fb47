import styled from 'styled-components';

export const WidgetWrapper = styled.div`
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 1rem;
`;

export const StaffList = styled.ul`
	list-style: none;
	padding: 0;
	margin: 0;
	max-height: 70vh;
	overflow-y: auto;
`;

export const StaffItem = styled.li`
	margin: 0 0 1rem;
`;

export const LoadingWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
`;

export const NoStaffMessage = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 2rem;
	font-size: 1rem;
	color: #637381;
`;

export const RosterCard = styled.div`
	border-radius: 4px;
	border: 1px solid #dfe3e8;
	padding: 16px;
	cursor: pointer;

	&:hover {
		.roster-card__header {
			text-decoration: underline;
		}
	}
`;

export const RosterCardHeader = styled.p`
	margin-bottom: 4px;
	font-size: 16px;
	line-height: 24px;
	font-weight: 700;
`;

export const RosterCardContentWrapper = styled.div`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		display: flex;
		justify-content: space-between;
		align-items: center;

		p {
			width: 50%;

			&:last-child {
				text-align: right;
			}
		}
	}
`;

export const RosterCardContent = styled.p`
	font-size: 14px;
	line-height: 22px;
	font-weight: 700;

	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-weight: normal;
	}
`;

export const RosterCardFooter = styled.p`
	font-size: 14px;
	line-height: 22px;
`;

import { useCallback, useRef } from 'react';

import { useFavoriteTeamsManager } from '@features/favorite-teams-manager';

import { PaginatedStaff, usePaginatedStaff } from '../model';
import { StaffCard } from './StaffCard';
import { LoadingWrapper, NoStaffMessage, StaffItem, StaffList, WidgetWrapper } from './styled';

export type Props = {
	eswId: string;
	search?: string | null;
};

export const StaffListWidget = ({ eswId, search }: Props) => {
	const { data: staff, loading, fetchNext } = usePaginatedStaff({ eswId, search });
	const { favoriteTeamsIds } = useFavoriteTeamsManager(eswId);

	const listRef = useRef<HTMLUListElement>(null);

	// Handle scroll to load more staff
	const handleScroll = useCallback(() => {
		if (!listRef.current) return;

		const { scrollTop, scrollHeight, clientHeight } = listRef.current;

		// If we're near the bottom, load more
		if (scrollTop + clientHeight >= scrollHeight - 100 && !loading) {
			fetchNext();
		}
	}, [fetchNext, loading]);

	// Add scroll event listener
	const handleScrollEvent = useCallback(() => {
		handleScroll();
	}, [handleScroll]);

	// Handle team modal opening (placeholder for now)
	const handleStaffHeaderClick = useCallback((staff: PaginatedStaff) => {
		console.log('Open team modal for staff team: ', staff.team_id);
		// TODO: Implement team modal functionality
	}, []);

	if (!staff && loading) {
		return (
			<LoadingWrapper>
				<div>Loading...</div>
			</LoadingWrapper>
		);
	}

	if (!staff || staff.length === 0) {
		return <NoStaffMessage>No staff found</NoStaffMessage>;
	}

	return (
		<WidgetWrapper>
			<StaffList ref={listRef} onScroll={handleScrollEvent}>
				{staff.map((staffMember) => (
					<StaffItem key={staffMember.staff_id}>
						<StaffCard
							staff={staffMember}
							belongsToFavouriteTeam={favoriteTeamsIds.includes(staffMember.team_id)} // TODO: Use hashmap for better performance
							onHeaderClick={handleStaffHeaderClick}
						/>
					</StaffItem>
				))}
				{loading && (
					<LoadingWrapper>
						<div>Loading more...</div>
					</LoadingWrapper>
				)}
			</StaffList>
		</WidgetWrapper>
	);
};

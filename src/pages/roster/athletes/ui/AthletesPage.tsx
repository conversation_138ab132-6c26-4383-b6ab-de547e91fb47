import { useParams, useSearchParams } from 'react-router';
import styled from 'styled-components';

import { AthletesListWidget } from '@widgets/athletes-list';

const AthletesPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
`;

const Title = styled.h3`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
	padding: 0 1rem;
`;

export const AthletesPage = () => {
	const { eswId } = useParams();
	const [searchParams] = useSearchParams();
	const search = searchParams.get('search');

	return (
		<AthletesPageWrapper>
			<Title>Athletes</Title>
			<AthletesListWidget eswId={eswId!} search={search} />
		</AthletesPageWrapper>
	);
};

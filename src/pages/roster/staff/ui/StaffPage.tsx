import { useParams, useSearchParams } from 'react-router';
import styled from 'styled-components';

import { StaffListWidget } from '@widgets/staff-list';

const StaffPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
`;

const Title = styled.h3`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
	padding: 0 1rem;
`;

export const StaffPage = () => {
	const { eswId } = useParams();
	const [searchParams] = useSearchParams();
	const search = searchParams.get('search');

	return (
		<StaffPageWrapper>
			<Title>Staff</Title>
			<StaffListWidget eswId={eswId!} search={search} />
		</StaffPageWrapper>
	);
};

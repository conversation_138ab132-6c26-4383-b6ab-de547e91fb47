import styled from 'styled-components';

const NotFoundWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	flex-direction: column;
	font-family: 'Public Sans', sans-serif;
`;

const Title = styled.h1`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

const Message = styled.p`
	color: ${({ theme }) => theme.colors.text};
	text-align: center;
	margin-bottom: 2rem;
`;

export const NotFoundPage = () => {
	return (
		<NotFoundWrapper>
			<Title>404 - Page Not Found</Title>
			<Message>The page you are looking for does not exist or has been moved.</Message>
		</NotFoundWrapper>
	);
};

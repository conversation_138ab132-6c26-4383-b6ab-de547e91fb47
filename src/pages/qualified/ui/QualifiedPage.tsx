import { useParams } from 'react-router';
import styled from 'styled-components';

import { QualifiedTeamsListWidget } from '@widgets/qualified-teams-list';

const QualifiedPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
	padding: 1rem;
`;

const Title = styled.h2`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

export const QualifiedPage = () => {
	const { eswId } = useParams();

	return (
		<QualifiedPageWrapper>
			<Title>Qualified Teams</Title>
			<QualifiedTeamsListWidget eswId={eswId!} search={''} />
		</QualifiedPageWrapper>
	);
};

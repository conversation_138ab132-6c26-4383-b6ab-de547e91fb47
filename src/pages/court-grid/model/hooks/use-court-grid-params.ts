import { useMemo } from 'react';
import { useParams } from 'react-router';

import { CourtGridParamsReturnType } from '@pages/court-grid/model';

import { DivisionDetails } from '@entities/event-overview';

import { ALL_DIVISIONS_KEY, USE_DIVISIONS_SUMMARIZED_TIME_RANGE } from '@shared/config';
import { buildDivisionsDaysTimeRangesMap } from '@shared/domain/event-overview';
import { atTimeOfDayUTC } from '@shared/lib/time';

export const useCourtGridParams = (
	divisions: DivisionDetails[] | null,
): CourtGridParamsReturnType | null => {
	const { divisionId, dayIndex, startTime, endTime } = useParams();

	return useMemo(() => {
		if (!divisions || !divisionId || !dayIndex || !startTime || !endTime) return null;

		const divisionsDaysTimeRangesMap = buildDivisionsDaysTimeRangesMap(divisions);
		const daysTimeRangesMap = divisionsDaysTimeRangesMap.get(
			USE_DIVISIONS_SUMMARIZED_TIME_RANGE ? ALL_DIVISIONS_KEY : divisionId!,
		)!;
		const days = Array.from(daysTimeRangesMap.keys());
		const day = days[+dayIndex!];
		const timeRange = daysTimeRangesMap.get(day)!;

		const after = atTimeOfDayUTC(day, startTime!);
		const before = atTimeOfDayUTC(day, endTime!);

		return {
			divisionId,
			day,
			days,
			timeRange,
			after,
			before,
			startTime: startTime!,
			endTime: endTime!,
		};
	}, [divisions, dayIndex, divisionId, endTime, startTime]);
};

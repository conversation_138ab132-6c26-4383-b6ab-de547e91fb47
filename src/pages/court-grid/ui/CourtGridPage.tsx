import { useCallback, useState } from 'react';
import { useNavigate, useParams } from 'react-router';

import { CourtGridBoardWidget } from '@widgets/court-grid-board';
import { CourtGridControlsWidget } from '@widgets/court-grid-controls';

import { useEventOverview } from '@entities/event-overview';

import { ALL_DIVISIONS_KEY } from '@shared/config';

import { useCourtGridParams } from '../model';
import { CourtGridPageWrapper, GridContainer, Title } from './styled';

export const CourtGridPage = () => {
	const navigate = useNavigate();
	const { eswId } = useParams();
	const { divisions, loading: eventLoading } = useEventOverview(eswId!);
	const params = useCourtGridParams(divisions);

	const [focusedMatchId, setFocusedMatchId] = useState<string | null>(null);
	const [gridStep, setGridStep] = useState(30);

	const updateUrl = useCallback(
		(divisionId: string, dayIndex: number, startTime: string, endTime: string) => {
			navigate(`/event/${eswId}/court-grid/${divisionId}/${dayIndex}/${startTime}/${endTime}`);
		},
		[eswId, navigate],
	);

	const handleDayChange = useCallback(
		(newDay: string) => {
			if (!params) return;
			const { divisionId, days, startTime, endTime } = params;
			updateUrl(divisionId, days.indexOf(newDay), startTime, endTime);
		},
		[params, updateUrl],
	);

	const handleStartTimeChange = useCallback(
		(newStartTime: string) => {
			if (!params) return;
			const { divisionId, day, days, endTime } = params;
			updateUrl(divisionId, days.indexOf(day), newStartTime, endTime);
		},
		[params, updateUrl],
	);

	const handleEndTimeChange = useCallback(
		(newEndTime: string) => {
			if (!params) return;
			const { divisionId, day, days, startTime } = params;
			updateUrl(divisionId, days.indexOf(day), startTime, newEndTime);
		},
		[params, updateUrl],
	);

	const handleDivisionChange = useCallback(
		(newDivisionId: string) => {
			if (!params) return;
			const { day, days, startTime, endTime } = params;
			updateUrl(newDivisionId, days.indexOf(day), startTime, endTime);
		},
		[params, updateUrl],
	);

	const handleGridStepChange = useCallback((newGridStep: number) => {
		setGridStep(newGridStep);
	}, []);

	const handleMatchClick = useCallback(
		(matchId: string) => {
			setFocusedMatchId(matchId === focusedMatchId ? null : matchId);
		},
		[focusedMatchId],
	);

	// Show loading state while event data is loading
	if (eventLoading || !params || !divisions) {
		return (
			<CourtGridPageWrapper>
				<Title>Court Grid</Title>
				<div>Loading...</div>
			</CourtGridPageWrapper>
		);
	}

	const { divisionId, startTime, endTime, day, days, timeRange, after, before } = params;

	return (
		<CourtGridPageWrapper>
			<Title>Court Grid</Title>

			<CourtGridControlsWidget
				divisions={divisions}
				selectedDivisionId={divisionId}
				selectedDay={day}
				availableDays={days}
				selectedStartTime={startTime}
				selectedEndTime={endTime}
				timeRange={timeRange}
				selectedGridStep={gridStep}
				onDivisionChange={handleDivisionChange}
				onDayChange={handleDayChange}
				onStartTimeChange={handleStartTimeChange}
				onEndTimeChange={handleEndTimeChange}
				onGridStepChange={handleGridStepChange}
			/>

			<GridContainer>
				<CourtGridBoardWidget
					eswId={eswId!}
					divisionId={divisionId === ALL_DIVISIONS_KEY ? null : divisionId}
					after={after}
					before={before}
					focusedMatchId={focusedMatchId}
					gridStep={gridStep}
					onMatchClick={handleMatchClick}
				/>
			</GridContainer>
		</CourtGridPageWrapper>
	);
};

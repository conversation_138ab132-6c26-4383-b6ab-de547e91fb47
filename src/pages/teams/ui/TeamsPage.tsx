import { useParams, useSearchParams } from 'react-router';
import styled from 'styled-components';

import { DivisionTeamsListWidget } from '@widgets/division-teams-list';

const TeamsPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
	padding: 1rem;
`;

const Title = styled.h2`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

const NavigationButtons = styled.div`
	display: flex;
	gap: 1rem;
	margin-bottom: 1rem;
`;

const NavButton = styled.button`
	padding: 0.5rem 1rem;
	border: 1px solid ${({ theme }) => theme.colors.primary || '#007bff'};
	background: white;
	color: ${({ theme }) => theme.colors.primary || '#007bff'};
	border-radius: 4px;
	cursor: pointer;
	text-decoration: none;

	&:hover {
		background: ${({ theme }) => theme.colors.primary || '#007bff'};
		color: white;
	}
`;

export const TeamsPage = () => {
	const { eswId, divisionId } = useParams();
	const [searchParams] = useSearchParams();
	const search = searchParams.get('search');

	const handleNavigateToStandings = () => {
		window.location.href = `/event/${eswId}/standings/${divisionId}`;
	};

	const handleNavigateToPoolBracket = () => {
		window.location.href = `/event/${eswId}/pool-bracket`;
	};

	return (
		<TeamsPageWrapper>
			<Title>Division Teams</Title>
			<NavigationButtons>
				<NavButton onClick={handleNavigateToStandings}>View Standings</NavButton>
				<NavButton onClick={handleNavigateToPoolBracket}>View Pool Brackets</NavButton>
			</NavigationButtons>
			<DivisionTeamsListWidget eswId={eswId!} divisionId={divisionId!} search={search} />
		</TeamsPageWrapper>
	);
};

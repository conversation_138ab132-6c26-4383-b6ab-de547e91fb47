import { useParams, useSearchParams } from 'react-router';
import styled from 'styled-components';

import { ClubsTeamsListWidget } from '@widgets/clubs-teams-list';

const ClubsTeamsPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
	padding: 1rem;
`;

const Title = styled.h2`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

export const ClubsTeamsPage = () => {
	const { eswId } = useParams();
	const [searchParams] = useSearchParams();
	const search = searchParams.get('search');

	return (
		<ClubsTeamsPageWrapper>
			<Title>Clubs & Teams</Title>
			<ClubsTeamsListWidget eswId={eswId!} search={search} />
		</ClubsTeamsPageWrapper>
	);
};

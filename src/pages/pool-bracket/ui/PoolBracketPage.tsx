import { useNavigate, useParams } from 'react-router';
import styled from 'styled-components';

import { DivisionRoundPoolBracketsListWidget } from '@widgets/division-round-pool-brackets-list';
import { DivisionRoundsSelectorWidget } from '@widgets/division-rounds-selector';

import { RoundDetails } from '@features/division-rounds-manager';

const PoolBracketPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
	padding: 1rem;
	gap: 1rem;
`;

const Title = styled.h2`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

const ContentWrapper = styled.div`
	display: flex;
	gap: 1rem;

	@media (max-width: 768px) {
		flex-direction: column;
	}
`;

const SelectorWrapper = styled.div`
	flex: 0 0 300px;

	@media (max-width: 768px) {
		flex: none;
	}
`;

const PoolBracketsWrapper = styled.div`
	flex: 1;
	min-width: 0;
`;

export const PoolBracketPage = () => {
	const { eswId, divisionId, roundId } = useParams();
	const navigate = useNavigate();

	const handleRoundSelect = (roundDetails: RoundDetails) => {
		// Navigate to the new round while keeping the same division
		navigate(`/event/${eswId}/pool-bracket/${divisionId}/round/${roundDetails.uuid}`);
	};

	return (
		<PoolBracketPageWrapper>
			<Title>Pool Bracket</Title>
			<ContentWrapper>
				<SelectorWrapper>
					<DivisionRoundsSelectorWidget
						eswId={eswId!}
						divisionId={divisionId!}
						roundId={roundId!}
						onRoundSelect={handleRoundSelect}
					/>
				</SelectorWrapper>
				<PoolBracketsWrapper>
					<DivisionRoundPoolBracketsListWidget
						eswId={eswId!}
						divisionId={divisionId!}
						roundId={roundId!}
					/>
				</PoolBracketsWrapper>
			</ContentWrapper>
		</PoolBracketPageWrapper>
	);
};

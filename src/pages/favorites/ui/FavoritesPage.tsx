import { useParams, useSearchParams } from 'react-router';
import styled from 'styled-components';

import { FavoriteTeamsListWidget } from '@widgets/favorite-teams-list';

import { FavoriteTeam } from '@entities/team';

const FavoritesPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
	padding: 1rem;
`;

const Title = styled.h2`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

export const FavoritesPage = () => {
	const { eswId } = useParams();
	const [searchParams] = useSearchParams();
	const search = searchParams.get('search');

	const handleTeamClick = (team: FavoriteTeam) => {
		// Demo implementation - in real app this would open team modal or navigate
		console.log('Team clicked:', team.team_name);
		// TODO: Implement team modal or navigation
	};

	return (
		<FavoritesPageWrapper>
			<Title>Favorite Teams</Title>
			<FavoriteTeamsListWidget eswId={eswId!} search={search} onTeamClick={handleTeamClick} />
		</FavoritesPageWrapper>
	);
};

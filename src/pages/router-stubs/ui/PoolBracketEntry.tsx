import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router';

import { useEventOverview } from '@entities/event-overview';

import { findDivisionById } from '@shared/domain/division';
import { findClosestRound } from '@shared/domain/round';

export const PoolBracketEntry = () => {
	// EswId is guaranteed by the parent route guard
	const { eswId, divisionId, roundId } = useParams();

	const { divisions, loading } = useEventOverview(eswId!);
	const navigate = useNavigate();

	useEffect(() => {
		if (loading || !divisions) return;
		// Identify the target division (by id or first one as fallback)
		const targetDivision = divisionId ? findDivisionById(divisions, divisionId) : divisions[0]; // first division as fallback
		if (!targetDivision?.division_id) {
			navigate(`/event/${eswId}`);
			return;
		}

		// Identify the target round (by id or closest one as fallback)
		const rounds = targetDivision.rounds ?? [];
		const targetRound = roundId ? rounds.find((r) => r.uuid === roundId) : findClosestRound(rounds);

		if (!targetRound?.uuid) {
			navigate(`/event/${eswId}`);
			return;
		}

		// Navigate to the canonical URL
		navigate(
			`/event/${eswId}/pool-bracket/${targetDivision.division_id}/round/${targetRound.uuid}`,
			{ replace: true },
		);
	}, [loading, divisions, divisionId, roundId, eswId, navigate]);

	if (!divisions || loading) {
		// If the data is still loading, we can show a loading spinner or placeholder
		return <div>Loading...</div>;
	}
	return null;
};

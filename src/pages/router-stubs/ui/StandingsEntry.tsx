import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router';

import { useEventOverview } from '@entities/event-overview';

export const StandingsEntry = () => {
	const eswId = useParams().eswId!; // EswId is guaranteed to be present due to the route guard

	const { divisions, loading } = useEventOverview(eswId); // Getting the event overview data to choose the first available division
	const navigate = useNavigate();

	useEffect(() => {
		if (!divisions || loading) return;
		// If there are no divisions, navigate to the event page
		if (!divisions?.length) navigate(`/event/${eswId}`);

		const [firstDivision] = divisions;
		if (!firstDivision?.division_id) {
			navigate(`/event/${eswId}`);
			return;
		}

		// Navigate to the canonical URL
		navigate(firstDivision.division_id, { replace: true }); // Navigates /event/:eswId/standings/:divisionId
	}, [divisions, loading, navigate, eswId]);

	if (!divisions || loading) {
		// If the data is still loading, we can show a loading spinner or placeholder
		return <div>Loading...</div>;
	}
	return null;
};

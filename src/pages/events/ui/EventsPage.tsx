import { addDays, startOfHour } from 'date-fns';
import { useCallback, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router';
import styled from 'styled-components';

import { EventsListWidget, PaginatedEvent } from '@widgets/events-list';

import { NavigationButtons } from '@shared/ui/components';

const EventsPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	font-family: 'Public Sans', sans-serif;
`;

const Header = styled.div`
	background-color: ${({ theme }) => theme.colors.primary};
	color: white;
	padding: 2rem 1rem;
	text-align: center;
`;

const Title = styled.h1`
	margin: 0 0 1rem 0;
	font-size: 2rem;
`;

const SearchContainer = styled.div`
	max-width: 500px;
	margin: 0 auto;
`;

const SearchInput = styled.input`
	width: 100%;
	padding: 0.75rem;
	border: none;
	border-radius: 4px;
	font-size: 1rem;
	outline: none;

	&::placeholder {
		color: #999;
	}
`;

const TabsContainer = styled.div`
	background-color: #f8f9fa;
	border-bottom: 1px solid #dfe3e8;
`;

const TabsList = styled.div`
	display: flex;
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 1rem;
`;

const Tab = styled.button<{ $isActive: boolean }>`
	padding: 1rem 1.5rem;
	border: none;
	background: ${({ $isActive }) => ($isActive ? '#fff' : 'transparent')};
	color: ${({ $isActive, theme }) => ($isActive ? theme.colors.primary : '#637381')};
	font-weight: ${({ $isActive }) => ($isActive ? '600' : '400')};
	border-bottom: ${({ $isActive, theme }) =>
		$isActive ? `2px solid ${theme.colors.primary}` : '2px solid transparent'};
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		background-color: ${({ $isActive }) => ($isActive ? '#fff' : '#f1f3f4')};
	}
`;

const ContentContainer = styled.div`
	flex: 1;
	max-width: 1200px;
	margin: 0 auto;
	width: 100%;
	padding: 2rem 1rem;
`;

const DemoSection = styled.div`
	margin-top: 2rem;
	padding: 1rem;
	background-color: #f5f5f5;
	border-radius: 4px;
	text-align: center;
`;

type TabType = 'current' | 'future' | 'past';

export const EventsPage = () => {
	const { tab } = useParams();
	const navigate = useNavigate();
	const [searchParams] = useSearchParams();
	const search = searchParams.get('search');

	const [activeTab, setActiveTab] = useState<TabType>((tab as TabType) || 'current');
	const [searchValue, setSearchValue] = useState(search || '');

	// Date calculations for filtering events
	const now = startOfHour(new Date()).toISOString();
	const next7Days = addDays(startOfHour(new Date()), 7).toISOString();

	const handleTabChange = (newTab: TabType) => {
		setActiveTab(newTab);
		navigate(`/events/${newTab}${searchValue ? `?search=${encodeURIComponent(searchValue)}` : ''}`);
	};

	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = e.target.value;
		setSearchValue(value);
		// Update URL with search parameter
		const params = new URLSearchParams();
		if (value) params.set('search', value);
		navigate(`/events/${activeTab}${params.toString() ? `?${params.toString()}` : ''}`);
	};

	const handleEventClick = useCallback(
		(event: PaginatedEvent) => {
			if (event.event_id) {
				navigate(`/event/${event.event_id}`);
			}
		},
		[navigate],
	);

	// Get filter parameters based on active tab
	const getEventFilters = () => {
		switch (activeTab) {
			case 'current':
				return {
					startBefore: next7Days,
					endAfter: now,
					asc: true,
				};
			case 'future':
				return {
					startAfter: now,
					asc: true,
				};
			case 'past':
				return {
					endBefore: now,
					asc: false,
				};
			default:
				return {};
		}
	};

	return (
		<EventsPageWrapper>
			<Header>
				<Title>Events</Title>
				<SearchContainer>
					<SearchInput
						type="text"
						placeholder="Search events..."
						value={searchValue}
						onChange={handleSearchChange}
					/>
				</SearchContainer>
			</Header>

			<TabsContainer>
				<TabsList>
					<Tab $isActive={activeTab === 'current'} onClick={() => handleTabChange('current')}>
						Current
					</Tab>
					<Tab $isActive={activeTab === 'future'} onClick={() => handleTabChange('future')}>
						Future
					</Tab>
					<Tab $isActive={activeTab === 'past'} onClick={() => handleTabChange('past')}>
						Past
					</Tab>
				</TabsList>
			</TabsContainer>

			<ContentContainer>
				<EventsListWidget
					search={searchValue || null}
					onEventClick={handleEventClick}
					{...getEventFilters()}
				/>

				<DemoSection>
					<NavigationButtons showEventLinks={false} showDivisionLinks={false} />
				</DemoSection>
			</ContentContainer>
		</EventsPageWrapper>
	);
};

import styled from 'styled-components';

const HomePageWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	flex-direction: column;
	font-family: 'Public Sans', sans-serif;
`;

const Title = styled.h1`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

const Subtitle = styled.p`
	color: ${({ theme }) => theme.colors.text};
`;

export const HomePage = () => {
	return (
		<HomePageWrapper>
			<Title>Hello World!</Title>
			<Subtitle>Welcome to the ESW Web Application</Subtitle>
		</HomePageWrapper>
	);
};

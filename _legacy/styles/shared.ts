import { styled } from 'styled-components';

import { ThemeT } from './theme';

export const IsBold = styled.span<{ $isBold: boolean }>`
	font-weight: ${(props) => (props.$isBold ? 'bold' : 'normal')};
`;

export const getCourtGridTypeAlert = (typeAlert?: 'warning' | 'error' | null) => {
	if (!typeAlert) {
		return '#637381';
	}

	if (typeAlert === 'warning') {
		return '#FFC107';
	}

	if (typeAlert === 'error') {
		return '#FF6161';
	}
};

export const Division__SearchBarAdmission = styled.a<ThemeT>`
	font-size: 10px;
	text-decoration: underline;
	line-height: 18px;
	color: #f9fafb;
	text-align: right;
	max-width: 90px;
	display: flex;
	align-items: center;
	gap: 4px;
	width: 100%;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		max-width: 140px;
		text-decoration: none;
		&:hover {
			text-decoration: underline;
		}
		img {
			width: 20px;
			height: 14px;
		}
	}
`;

export const Division__MainNavWrapper = styled.div<ThemeT>`
	display: flex;
	justify-content: space-between;
`;
export const StyledNotFound = styled.div<ThemeT>`
	color: ${(props) => props.theme.colors.light};
	text-align: center;
	font-size: 16px;
	line-height: 24px;
	position: absolute;
	top: 200px;
	width: 600px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		width: 100%;
	}
`;

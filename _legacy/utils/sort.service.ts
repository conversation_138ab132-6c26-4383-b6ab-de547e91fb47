export type RosterStaffRole = {
	[key: string]: unknown;
	role_name?: string;
};
export class SortService {
	private roleOrder: string[] = [];
	constructor() {
		this.roleOrder = [
			'Head Coach',
			'Asst Coach',
			'Team Representative',
			'Manager',
			'<PERSON><PERSON><PERSON>',
			'Club Director',
			'Asst Director',
		];
	}
	roleComparator(a: <PERSON><PERSON><PERSON>StaffRole, b: RosterStaffRole) {
		const roleIndexA = this.roleOrder.indexOf(a.role_name || '');
		const roleIndexB = this.roleOrder.indexOf(b.role_name || '');

		if (roleIndexA !== -1 && roleIndexB !== -1) {
			return roleIndexA - roleIndexB;
		}
		if (roleIndexA !== -1) {
			return -1;
		}
		if (roleIndexB !== -1) {
			return 1;
		}
		return 0;
	}
}

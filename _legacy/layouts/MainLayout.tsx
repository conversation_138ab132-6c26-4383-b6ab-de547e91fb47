import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { Outlet } from 'react-router-dom';

import { Footer } from '@/components/Footer';
import { Header } from '@/components/Header';
import { OldEventsButton } from '@/components/OldEventsButton';

import { MainLayout__Content, MainLayout__Wrapper } from './styled';

export const MainLayout = () => {
	const { breakPont } = useCurrentSize();
	return (
		<MainLayout__Wrapper>
			<Header />
			<MainLayout__Content>
				<Outlet />
			</MainLayout__Content>
			{breakPont !== 'small' && <OldEventsButton />}
			{breakPont !== 'small' && <Footer />}
		</MainLayout__Wrapper>
	);
};

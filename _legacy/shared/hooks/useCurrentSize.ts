import { useLayoutEffect, useState } from 'react';

import { theme } from '@/styles/theme';

type BreakpointKey = keyof typeof theme.breakpoints;

export const useCurrentSize = () => {
	const [breakPont, setBreakPont] = useState<BreakpointKey>();
	const [browserHeight, setBrowserHeight] = useState(window.innerHeight);
	useLayoutEffect(() => {
		const getCurrentBreakpoint = () => {
			setBrowserHeight(window.innerHeight);
			const currentWidth = window.innerWidth;
			const { breakpoints } = theme;
			let result: keyof typeof theme.breakpoints = 'large';
			for (const key of Object.keys(breakpoints)) {
				const breakpoint = parseInt(breakpoints[key as BreakpointKey]);
				if (currentWidth <= breakpoint) {
					result = key as BreakpointKey;
					break;
				}
			}

			setBreakPont(result);
		};
		getCurrentBreakpoint();

		window.addEventListener('resize', getCurrentBreakpoint);

		return () => {
			window.removeEventListener('resize', getCurrentBreakpoint);
		};
	}, []);

	return { breakPont, browserHeight };
};

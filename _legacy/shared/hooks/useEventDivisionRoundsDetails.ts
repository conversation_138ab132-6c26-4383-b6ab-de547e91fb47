import { useCallback, useMemo } from 'react';

import { getMaxTime, getMinTime } from '@/utils/time';

import { EventDivisionDetails, useEventDivisionDetails } from './useEventDivisionDetails';

type DivisionRound = EventDivisionDetails['rounds'][number];
type GroupedRound = Omit<DivisionRound, 'uuid'>;

const getRoundGroupKey = (round: DivisionRound) => round.short_name || round.uuid; // It should never fall back to uuid, but just in case

export const useEventDivisionRoundsDetails = (divisionId?: string) => {
	const { division, loading } = useEventDivisionDetails(divisionId);

	const [roundsMap, groupedRoundsMap] = useMemo(() => {
		const roundsMap = new Map<string, DivisionRound>(); // round_id ⇒ round
		const groupedRoundsMap = new Map<string, GroupedRound>(); // group key ⇒ merged round info

		if (!division) return [roundsMap, groupedRoundsMap];

		division.rounds.forEach((round) => {
			const roundId = round.uuid;
			const groupKey = getRoundGroupKey(round);
			if (!groupedRoundsMap.has(groupKey)) {
				groupedRoundsMap.set(groupKey, round); // if the group doesn't exist, create it
			} else {
				const existing = groupedRoundsMap.get(groupKey)!;
				groupedRoundsMap.set(groupKey, {
					...existing, // if the group exists, merge the round timestamps
					first_match_start: getMinTime(existing.first_match_start, round.first_match_start),
					last_match_start: getMaxTime(existing.last_match_start, round.last_match_start),
				});
			}
			roundsMap.set(roundId, round);
		});

		return [roundsMap, groupedRoundsMap];
	}, [division]);

	const groupedRounds = useMemo<GroupedRound[]>(
		() => Array.from(groupedRoundsMap.values()),
		[groupedRoundsMap],
	);

	const getGroupedRoundByRoundId = useCallback(
		(roundId: string): GroupedRound | null => {
			const round = roundsMap.get(roundId);
			if (!round) return null;
			const groupKey = getRoundGroupKey(round);
			return groupedRoundsMap.get(groupKey) ?? null;
		},
		[groupedRoundsMap, roundsMap],
	);

	return {
		loading,
		rounds: division?.rounds ?? [],
		roundsMap,
		groupedRounds,
		groupedRoundsMap,
		getGroupedRoundByRoundId,
		getRoundGroupKey,
	};
};

import { useCallback, useState } from 'react';

import { BracketModal } from '@/components/BracketModal';

import {
	DivisionPool,
	PoolsQuery,
	Team as TeamEntity,
	usePoolsLazyQuery,
} from '@/generated/graphql';

import { useEventDetails } from './useEventDetails';

type Team = Pick<TeamEntity, 'team_id' | 'division_id'>;

export const useBracketModal = (openPoolModal: (_teamId: string) => void) => {
	const { eswId } = useEventDetails();
	const [modalTeamId, setModalTeamId] = useState<string | null>(null);
	const [currentPool, setCurrentPool] = useState<DivisionPool | null>(null);

	const [getPools, { loading }] = usePoolsLazyQuery();

	const openBracketModal = useCallback(
		({ team_id, division_id, bracketId }: Team & { bracketId?: string }) => {
			setCurrentPool(null);
			getPools({
				variables: {
					id: eswId,
					divisionId: division_id,
				},
				fetchPolicy: 'network-only',
				onCompleted(data) {
					if (data) {
						const groupedRounds = data.pools.reduce(
							(acc, el) => {
								const { r_uuid, ...rest } = el;
								if (!acc[r_uuid!] && el.uuid) {
									acc[r_uuid!] = [];
								}
								if (el.uuid) {
									acc[r_uuid!].push({ r_uuid, ...rest });
								}
								return acc;
							},
							{} as Record<string, PoolsQuery['pools']>,
						);

						const result = Object.values(groupedRounds);

						const pools: DivisionPool[] = [];
						result.forEach((round) => {
							round.forEach((pool) => {
								pool.teams?.forEach((team) => {
									if (String(team.opponent_team_id) === team_id) {
										pools.push(pool as DivisionPool);
									}
								});
								// If team1 and team2 doesn't exist
								if (!pools.length && pool.uuid === bracketId) {
									pools.push(pool as DivisionPool);
								}
							});
						});

						const pool = pools.find((pool) => pool.uuid === bracketId);

						if (!pool) return;
						if (!pool.is_pool) {
							setCurrentPool(pool);
							setModalTeamId(team_id);
						} else {
							openPoolModal(team_id);
						}
					}
				},
			});
		},
		[eswId, openPoolModal, getPools],
	);

	const bracketModalElement =
		modalTeamId && currentPool ? (
			<BracketModal
				key={modalTeamId}
				poolOfTeam={currentPool}
				bracketRounds={[currentPool]}
				poolId={currentPool.uuid!}
				close={() => setModalTeamId(null)}
			/>
		) : null;

	return {
		bracketModalElement,
		bracketDataLoading: loading,
		openBracketModal,
	};
};

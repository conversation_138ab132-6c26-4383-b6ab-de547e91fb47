import { useFavoriteTeamsIdsStore } from '@/store/favoriteTeamsIds.store';
import {
	LocalFavoriteClub,
	LocalFavoriteTeam,
	favoriteTeamsService,
} from '@services/favoriteTeams.service';
import { useEffect } from 'react';

import { useEventDetails } from './useEventDetails';

export const useFavoriteTeams = () => {
	const { eswId } = useEventDetails();
	const { favoriteTeamsIds, setFavoriteTeamsIds } = useFavoriteTeamsIdsStore();

	useEffect(() => {
		setFavoriteTeamsIds(favoriteTeamsService.getFavoriteTeamsIds(eswId));
	}, [eswId, setFavoriteTeamsIds]);

	const toggleTeamFavorite = (team: LocalFavoriteTeam) => {
		favoriteTeamsService.toggleTeamFavorite(eswId, team);
		setFavoriteTeamsIds(favoriteTeamsService.getFavoriteTeamsIds(eswId));
	};

	const toggleClubTeamsFavorite = (club: LocalFavoriteClub) => {
		favoriteTeamsService.toggleClubTeamsFavorite(eswId, club);
		setFavoriteTeamsIds(favoriteTeamsService.getFavoriteTeamsIds(eswId));
	};

	return {
		favoriteTeamsIds,
		toggleTeamFavorite,
		toggleClubTeamsFavorite,
	};
};

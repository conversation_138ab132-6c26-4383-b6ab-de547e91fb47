import { gql } from '@apollo/client';

export const GET_COURTS_RANGE_MATCHES = gql`
	query CourtsRangeMatches(
		$eswId: ID!
		$after: String!
		$before: String!
		$divisionId: ID
		$uniqKey: String!
	) {
		dayRangeMatches(eventKey: $eswId, after: $after, before: $before, divisionId: $divisionId) {
			items {
				team_id
				opponent_id
				ref_team_id
				match_id
				court_id
				is_tb
				match_name
				division_id
				division_name
				secs_start
				secs_end
				secs_finished
				results {
					winner_team {
						scores
						roster_team_id
					}
				}
				external {
					team_display_name
					opponent_display_name
					ref_team_display_name
					pool_bracket_info {
						is_pool
						uuid
					}
					team_pb_seed {
						id
					}
					opponent_pb_seed {
						id
					}
				}
			}
			filter_info {
				is_filtered
			}
		}
		relatedCourts(uniqKey: $uniqKey) {
			uuid
			name
			short_name
		}
	}
`;

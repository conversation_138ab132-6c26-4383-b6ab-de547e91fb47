import { gql } from '@apollo/client';

export const GET_DIVISION_OVERVIEW = gql`
	query DivisionOverview($eswId: ID!, $divisionId: ID!) {
		divisionPoolBrackets(eventKey: $eswId, divisionId: $divisionId) {
			... on Pool {
				is_pool
				uuid
				round_id
				sort_priority
				short_name
				display_name
				date_start
				division_id
				settings {
					SetCount
					PlayAllSets
					WinningPoints
				}
				external {
					courts_short_info {
						uuid
						short_name
					}
				}
				teams {
					team_id
					team_name
					master_team_id
					extra {
						show_accepted_bid
						show_previously_accepted_bid
					}
					division_standing {
						seed
						heading
						heading_priority
					}
					pool_bracket_stat {
						sets_pct
						sets_won
						sets_lost
						matches_won
						matches_lost
						points_ratio
					}
				}
				matches {
					team_id
					opponent_id
					ref_team_id
					match_id
					match_name
					match_number
					secs_start
					secs_end
					secs_finished
					source {
						team {
							id
							name
							type
							seed
							overallSeed
							reseedRank
						}
						opponent {
							id
							name
							type
							seed
							overallSeed
							reseedRank
						}
						ref {
							id
							name
							type
							seed
							overallSeed
							reseedRank
						}
					}
					results {
						sets
						winner_team {
							scores
							roster_team_id
						}
					}
					finishes {
						winner {
							team_name
							team_id
							next_match {
								court
								match_id
								week_day
								secs_start
								display_name
								start_time_string
								external {
									pool_bracket_info {
										is_pool
										uuid
									}
								}
							}
							next_ref {
								court
								match_id
								week_day
								secs_start
								display_name
								start_time_string
								external {
									pool_bracket_info {
										is_pool
										uuid
									}
								}
							}
						}
						looser {
							team_name
							team_id
							next_match {
								court
								match_id
								week_day
								secs_start
								display_name
								start_time_string
								external {
									pool_bracket_info {
										is_pool
										uuid
									}
								}
							}
							next_ref {
								court
								match_id
								week_day
								secs_start
								display_name
								start_time_string
								external {
									pool_bracket_info {
										is_pool
										uuid
									}
								}
							}
						}
					}
					external {
						team_pb_seed {
							id
							name
							type
							seed
							overallSeed
							reseedRank
						}
						opponent_pb_seed {
							id
							name
							type
							seed
							overallSeed
							reseedRank
						}
						court_info {
							uuid
							short_name
						}
						team_display_name
						opponent_display_name
						ref_team_display_name
					}
				}
				pb_finishes {
					team_name
					team_id
					next_match {
						court
						match_id
						week_day
						secs_start
						display_name
						start_time_string
						external {
							pool_bracket_info {
								is_pool
								uuid
							}
						}
					}
					next_ref {
						court
						match_id
						week_day
						secs_start
						display_name
						start_time_string
						external {
							pool_bracket_info {
								uuid
								is_pool
							}
						}
					}
				}
			}
			... on Bracket {
				is_pool
				uuid
				round_id
				sort_priority
				short_name
				display_name
				date_start
				division_id
				settings {
					SetCount
					PlayAllSets
					WinningPoints
				}
				external {
					courts_short_info {
						uuid
						short_name
					}
				}
				teams {
					team_id
					team_name
					master_team_id
					extra {
						show_accepted_bid
						show_previously_accepted_bid
					}
					division_standing {
						seed
						heading
						heading_priority
					}
					pool_bracket_stat {
						sets_pct
						sets_won
						sets_lost
						matches_won
						matches_lost
						points_ratio
					}
				}
				matches {
					team_id
					opponent_id
					ref_team_id
					match_id
					match_name
					match_number
					secs_start
					secs_end
					secs_finished
					source {
						team {
							id
							name
							type
							seed
							overallSeed
							reseedRank
						}
						opponent {
							id
							name
							type
							seed
							overallSeed
							reseedRank
						}
						ref {
							id
							name
							type
							seed
							overallSeed
							reseedRank
						}
					}
					results {
						sets
						winner_team {
							scores
							roster_team_id
						}
					}
					finishes {
						winner {
							team_name
							team_id
							next_match {
								court
								match_id
								week_day
								secs_start
								display_name
								start_time_string
								external {
									pool_bracket_info {
										is_pool
										uuid
									}
								}
							}
							next_ref {
								court
								match_id
								week_day
								secs_start
								display_name
								start_time_string
								external {
									pool_bracket_info {
										is_pool
										uuid
									}
								}
							}
						}
						looser {
							team_name
							team_id
							next_match {
								court
								match_id
								week_day
								secs_start
								display_name
								start_time_string
								external {
									pool_bracket_info {
										is_pool
										uuid
									}
								}
							}
							next_ref {
								court
								match_id
								week_day
								secs_start
								display_name
								start_time_string
								external {
									pool_bracket_info {
										is_pool
										uuid
									}
								}
							}
						}
					}
					external {
						team_pb_seed {
							id
							name
							type
							seed
							overallSeed
							reseedRank
						}
						opponent_pb_seed {
							id
							name
							type
							seed
							overallSeed
							reseedRank
						}
						court_info {
							uuid
							short_name
						}
						team_display_name
						opponent_display_name
						ref_team_display_name
					}
				}
				pb_finishes {
					team_name
					team_id
					next_match {
						court
						match_id
						week_day
						secs_start
						display_name
						start_time_string
						external {
							pool_bracket_info {
								is_pool
								uuid
							}
						}
					}
					next_ref {
						court
						match_id
						week_day
						secs_start
						display_name
						start_time_string
						external {
							pool_bracket_info {
								uuid
								is_pool
							}
						}
					}
				}
			}
		}
	}
`;

import { gql } from '@apollo/client';

export const GET_EVENT = gql`
	query CourtMatches($id: ID!, $day: String!, $hour: String!, $hours: String!, $division: String!) {
		courtMatches(id: $id, day: $day, hour: $hour, hours: $hours, division: $division) {
			divisions {
				division_id
				division_name
				gender
				sort_order
				max_age
				level_sort_order
				level
			}
			hours {
				time
				time12
				default
			}
			courts {
				court_id
				court_name
				event_id
				short_name
				matches {
					match_id
					match_name
					division_id
					division_name
					division_short_name
					date_start
					date_end
					secs_finished
					team1_roster_id
					team2_roster_id
					team_1_name
					team_2_name
					team_ref_name
					color
					results {
						winner
						team1 {
							scores
						}
						team2 {
							scores
						}
					}
				}
			}
		}
	}
`;

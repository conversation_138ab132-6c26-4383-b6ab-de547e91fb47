import { gql } from '@apollo/client';

export const GET_EVENT_WITH_ROSTER = gql`
	query EventWithRoster($id: ID!) {
		roster(id: $id) {
			event_id
			event_name
			state
			club_name
			organization_code
			team_name
			roster_team_id
			roster_athletes {
				id
				first
				last
				short_position
				uniform
				gradyear
			}
			roster_staff {
				id
				first
				last
				role_name
				sort_order
			}
		}
		event(id: $id) {
			event_id
			hide_seeds
			name
			long_name
			days
			city
			state
			event_notes
			address
			is_with_prev_qual
			tickets_published
			tickets_code
			is_require_recipient_name_for_each_ticket
			locations {
				location_name
			}
			divisions {
				gender
				name
				event_id
				short_name
				teams_count
				division_id
			}
			clubs {
				club_name
				teams_count
				roster_club_id
				state
				# club_code
			}
			athletes {
				age
				gender
				first
				last
				team_organization_code
				team_name
				jersey
				club_name
				division_id
				roster_team_id
			}
			teams {
				division_name
				division_short_name
				club_name
				# club_state
				team_name
				organization_code
				roster_team_id
				roster_club_id
				division_id
				gender
			}
			teams_settings {
				sort_by
				hide_standings
			}
		}
	}
`;

export const GET_EVENT_WITH_ROSTER_FOR_ABOUT_PAGE = gql`
	query EventWithRosterForAboutPage($id: ID!) {
		roster(id: $id) {
			roster_athletes {
				id
			}
			roster_staff {
				id
			}
		}
		event(id: $id) {
			event_id
			has_rosters
			hide_seeds
			name
			long_name
			days
			city
			state
			event_notes
			address
			is_with_prev_qual
			tickets_published
			tickets_code
			is_require_recipient_name_for_each_ticket
			locations {
				location_name
			}
			divisions {
				division_id
			}
			clubs {
				roster_club_id
			}
			athletes {
				first
				last
			}
			teams {
				roster_team_id
			}
			teams_settings {
				sort_by
				hide_standings
			}
		}
	}
`;
export const GET_EVENT_WITH_ROSTER_FOR_ROSTER_PAGE = gql`
	query EventWithRosterForRosterPage($id: ID!) {
		roster(id: $id) {
			event_id
			event_name
			state
			club_name
			organization_code
			team_name
			roster_team_id
			roster_athletes {
				id
				first
				last
				short_position
				uniform
				gradyear
			}
			roster_staff {
				id
				first
				last
				role_name
				sort_order
			}
		}
		event(id: $id) {
			event_id
			hide_seeds
			name
			long_name
			days
			city
			state
			event_notes
			address
			is_with_prev_qual
			tickets_published
			tickets_code
			is_require_recipient_name_for_each_ticket
			locations {
				location_name
			}
			divisions {
				division_id
			}
			clubs {
				roster_club_id
			}
			athletes {
				first
				last
			}
			teams {
				roster_team_id
			}
			teams_settings {
				sort_by
				hide_standings
			}
		}
	}
`;
export const GET_DAYS_OF_EVENT = gql`
	query DaysOfEvent($id: ID!) {
		event(id: $id) {
			days
		}
	}
`;

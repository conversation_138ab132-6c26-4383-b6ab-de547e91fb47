import { gql } from '@apollo/client';

export const GET_EVENT_DETAILS = gql`
	query EventDetails($eswId: ID!) {
		event(id: $eswId) {
			id
			long_name
			city
			state
			tickets_published
			schedule_published
			is_require_recipient_name_for_each_ticket
			tickets_code
			hide_seeds
			days
			has_rosters
			is_with_prev_qual
			event_notes
			address
			sport_sanctioning
			locations {
				location_name
			}
			teams_settings {
				baller_tv_available
				hide_standings
				sort_by
			}
		}
		divisions(eventKey: $eswId) {
			division_id
			name
			teams_count
			short_name
			has_flow_chart
			media(filterFileTypes: ["flowchart"]) {
				media_id
				division_id
				file_type
				path
			}
			matches_time_ranges {
				day
				start_time
				end_time
			}
			rounds {
				uuid
				division_id
				sort_priority
				name
				short_name
				first_match_start
				last_match_start
			}
		}
	}
`;

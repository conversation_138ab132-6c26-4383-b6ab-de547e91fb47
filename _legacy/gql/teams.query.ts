import { gql } from '@apollo/client';

export const GET_DIVISIONS_QUALIFIED_TEAMS = gql`
	query DivisionsQualifiedTeams($eswId: ID!) {
		divisions(eventKey: $eswId) {
			division_id
			name
			qualified_teams {
				team_id
				team_name
				extra {
					prev_qual_age
					prev_qual_division
					earned_at
				}
			}
		}
	}
`;

export const GET_FAVORITE_TEAMS_STANDING = gql`
	query FavoriteTeamsStanding($eswId: ID!, $teamsIds: [ID!]!, $search: String) {
		favoriteTeams(eventKey: $eswId, teamsIds: $teamsIds, search: $search) {
			club_id
			team_id
			team_name
			team_code
			division_id
			division_name
			next_match {
				secs_start
				external {
					pool_bracket_info {
						is_pool
						uuid
					}
					court_info {
						short_name
					}
				}
			}
			division_standing {
				matches_won
				matches_lost
				sets_won
				sets_lost
				sets_pct
				points_ratio
				rank
				seed
				heading
				heading_priority
			}
		}
	}
`;

export const GET_PAGINATED_DIVISION_TEAMS = gql`
	query PaginatedDivisionTeams(
		$eswId: ID!
		$divisionId: ID!
		$page: Float!
		$pageSize: Float!
		$search: String
	) {
		paginatedDivisionTeams(
			eventKey: $eswId
			divisionId: $divisionId
			page: $page
			pageSize: $pageSize
			search: $search
		) {
			items {
				team_id
				team_name
				division_id
				next_match {
					secs_start
					external {
						opponent_display_name
						court_info {
							short_name
						}
						pool_bracket_info {
							is_pool
							uuid
						}
					}
				}
				division_standing {
					matches_won
					matches_lost
					sets_won
					sets_lost
					rank
				}
			}
			page_info {
				page
				page_size
				page_count
				item_count
			}
		}
	}
`;

export const GET_DIVISION_TEAMS_STANDING = gql`
	query DivisionTeamsStanding($eswId: ID!, $divisionId: ID!) {
		divisionTeamsStanding(eventKey: $eswId, divisionId: $divisionId) {
			team_id
			team_name
			team_code
			extra {
				show_previously_accepted_bid
				show_accepted_bid
			}
			division_standing {
				matches_won
				matches_lost
				sets_won
				sets_lost
				sets_pct
				points_ratio
				points
				rank
				seed
				heading
			}
		}
	}
`;

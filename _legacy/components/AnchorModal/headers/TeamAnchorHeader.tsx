import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import closeIcon from '@assets/close-black-icon.svg';
import favoriteIcon from '@assets/favoriteCheckedBig-icon.svg';
import outFavoriteIcon from '@assets/favoriteUnchecked-icon.svg';
import { useEffect } from 'react';
import { StringParam, useQueryParams } from 'use-query-params';

import {
	AnchorModal__HeaderCloseButton,
	AnchorTeamModal__Header,
	AnchorTeamModal__HeaderClubName,
	AnchorTeamModal__HeaderTeamCode,
	AnchorTeamModal__HeaderTeamName,
	AnchorTeamModal__HeaderTeamStreamLink,
	AnchorTeamModal__HeaderTeamStreamLinkWrapper,
} from '../styled';
import teamCameraIcon from './img/team-camera.svg';

type PropsT = {
	isFavorite?: boolean;
	close: () => void;
	teamName: string;
	code: string;
	clubName: string;
	state: string;
	toggleFavorite: () => void;
	masterTeamId?: number | null;
	refreshButton?: () => JSX.Element;
};

export const TeamAnchorHeader = ({
	close,
	teamName,
	code,
	clubName,
	state,
	isFavorite,
	toggleFavorite,
	masterTeamId,
	refreshButton,
}: PropsT) => {
	const { event } = useEventDetails();
	const { breakPont } = useCurrentSize();

	const [, setQueryParams] = useQueryParams({
		modalTeam: StringParam,
	});

	useEffect(() => {
		setQueryParams({ modalTeam: 'open' });
	}, [setQueryParams]);

	return (
		<AnchorTeamModal__Header
			$isShowStreamForMobile={!!event?.teams_settings?.baller_tv_available && breakPont === 'small'}
		>
			<AnchorTeamModal__HeaderTeamName>
				<div>
					<img src={isFavorite ? favoriteIcon : outFavoriteIcon} alt="" onClick={toggleFavorite} />
					{teamName}, <AnchorTeamModal__HeaderTeamCode>{code}</AnchorTeamModal__HeaderTeamCode>
				</div>
				{
					<AnchorTeamModal__HeaderTeamStreamLinkWrapper>
						{event?.teams_settings?.baller_tv_available && breakPont !== 'small' && (
							<AnchorTeamModal__HeaderTeamStreamLink
								href={`${import.meta.env.VITE_BALLERTV_URL}/teams?sport_wrench_team_id=${masterTeamId}`}
								target="_blank"
							>
								Watch Live <img src={teamCameraIcon} alt="" />
							</AnchorTeamModal__HeaderTeamStreamLink>
						)}
						{refreshButton && breakPont !== 'small' && refreshButton()}
					</AnchorTeamModal__HeaderTeamStreamLinkWrapper>
				}
			</AnchorTeamModal__HeaderTeamName>
			<AnchorTeamModal__HeaderClubName>
				{clubName}, {state}
			</AnchorTeamModal__HeaderClubName>
			{
				<AnchorTeamModal__HeaderTeamStreamLinkWrapper>
					{event?.teams_settings?.baller_tv_available && breakPont === 'small' && (
						<AnchorTeamModal__HeaderTeamStreamLink
							href={`${import.meta.env.VITE_BALLERTV_URL}/teams?sport_wrench_team_id=${masterTeamId}`}
							target="_blank"
						>
							<img src={teamCameraIcon} alt="" /> Watch Live
						</AnchorTeamModal__HeaderTeamStreamLink>
					)}
					{!event?.teams_settings?.baller_tv_available && breakPont === 'small' && <div />}
					{refreshButton && breakPont === 'small' && refreshButton()}
				</AnchorTeamModal__HeaderTeamStreamLinkWrapper>
			}
			<AnchorModal__HeaderCloseButton
				onClick={close}
				$isTeamModal
				$isShowStreamForMobile={
					!!event?.teams_settings?.baller_tv_available && breakPont === 'small'
				}
			>
				<img src={closeIcon} alt="" />
			</AnchorModal__HeaderCloseButton>
			{/* {refreshButton && refreshButton()} */}
		</AnchorTeamModal__Header>
	);
};

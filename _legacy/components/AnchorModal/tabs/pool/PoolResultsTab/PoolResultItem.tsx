import { useState } from 'react';

import { getFormateTime } from '@/utils/time';

import { ResultPoolTab__ShowMoreButton, ResultPoolTab__ShowMoreContent } from '../../styled';

type PropsT = {
	courtName: string;
	refName: string;
	date: string;
	isShowShowMoreForItem?: boolean;
	isShowAllDetails?: boolean;
};
export const PoolResultItem = ({
	courtName,
	refName,
	date,
	isShowAllDetails,
	isShowShowMoreForItem,
}: PropsT) => {
	const [isOpenMoreInfo, setIsOpenMoreInfo] = useState(false);
	const toggleMoreInfo = () => setIsOpenMoreInfo(!isOpenMoreInfo);
	const formattedDate = date && getFormateTime({ time: date, format: 'EEE, h:mmaaa' });
	const amPmLowercase = formattedDate && formattedDate.slice(-2).toLocaleLowerCase();
	const formattedDateLowercase = formattedDate && formattedDate.slice(0, -2) + amPmLowercase;

	if (isShowShowMoreForItem) {
		return (
			<ResultPoolTab__ShowMoreButton onClick={toggleMoreInfo}>
				{isOpenMoreInfo && (
					<ResultPoolTab__ShowMoreContent>
						<div>
							<p>
								<i>Work: {refName}</i>
							</p>
						</div>
						<div>
							<p>{formattedDateLowercase}</p>
							<p>{courtName}</p>
						</div>
					</ResultPoolTab__ShowMoreContent>
				)}
				<span>Show {isOpenMoreInfo ? 'less' : 'more'}</span>
			</ResultPoolTab__ShowMoreButton>
		);
	}
	if (isShowAllDetails) {
		return (
			<ResultPoolTab__ShowMoreButton>
				<ResultPoolTab__ShowMoreContent>
					<div>
						<p>
							<i>Work: {refName}</i>
						</p>
					</div>
					<div>
						<p>{formattedDateLowercase}</p>
						<p>{courtName}</p>
					</div>
				</ResultPoolTab__ShowMoreContent>
			</ResultPoolTab__ShowMoreButton>
		);
	}
	return null;
};

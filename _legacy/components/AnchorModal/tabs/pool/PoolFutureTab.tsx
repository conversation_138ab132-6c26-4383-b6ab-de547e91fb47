import { useBracketModal } from '@/shared/hooks/useBracketModal';
import { usePoolModal } from '@/shared/hooks/usePoolModal';
import { ClubsWithTeamsArray } from '@/shared/types/clubWithTeams.types';
import { addOrdinalSuffix } from '@/utils';
import { Fragment } from 'react';

import { Loader } from '@/components/Loader';

import { PoolOrBracketShortInfo, TeamAdvancement } from '@/generated/graphql';

import {
	FutureTab__Container,
	ScheduleFutureTab_Item,
	ScheduleFutureTab__CourtName,
	ScheduleFutureTab__ItemCell,
	ScheduleFutureTab__List,
	StandingsTab__Header,
	Tab__Wrapper,
} from '../styled';

type PropsT = {
	teamList: TeamAdvancement[];
	clubsWithTeams: ClubsWithTeamsArray[];
	isBracket?: boolean;
	divisionId?: number | null;
};

export const PoolFutureTab = ({ teamList, isBracket, divisionId }: PropsT) => {
	const formatNextMatchLabel = (team: TeamAdvancement) => {
		if (team.next_match?.display_name === 'Tournament play complete') {
			return team.next_match.display_name;
		}
		return [team.team_name, team.next_match?.display_name].filter(Boolean).join(' Plays ');
	};

	const { poolModalElement, openPoolModal, poolDataLoading } = usePoolModal();

	const { bracketModalElement, openBracketModal, bracketDataLoading } =
		useBracketModal(openPoolModal);

	const loading = poolDataLoading || bracketDataLoading;

	const openPoolBracketModal = (poolBracketInfo?: PoolOrBracketShortInfo, teamId?: string) => {
		poolBracketInfo?.is_pool
			? openPoolModal(String(teamId), poolBracketInfo.uuid)
			: openBracketModal({
					team_id: teamId!,
					bracketId: poolBracketInfo?.uuid,
					division_id: String(divisionId) || '',
				});
	};

	return (
		<>
			{loading && <Loader />}
			{bracketModalElement}
			{poolModalElement}
			<Tab__Wrapper>
				<FutureTab__Container>
					<StandingsTab__Header>
						<span>Future</span>
					</StandingsTab__Header>
					<ScheduleFutureTab__List>
						{teamList.map((team, index) => {
							return (
								<Fragment key={`pool-future-${index}`}>
									{team.next_match?.match_id && (
										<ScheduleFutureTab_Item
											onClick={() => {
												team.next_match?.external?.pool_bracket_info &&
													openPoolBracketModal(
														team.next_match.external.pool_bracket_info,
														String(team.team_id),
													);
											}}
										>
											<ScheduleFutureTab__ItemCell $isBracket>
												{!isBracket && <strong>{addOrdinalSuffix(index + 1)}</strong>}
												{isBracket && !index && (
													<>
														<strong>Winner</strong>
													</>
												)}
											</ScheduleFutureTab__ItemCell>
											<ScheduleFutureTab__ItemCell>
												<div>
													{isBracket && !index && 'Plays'} {formatNextMatchLabel(team)}
												</div>
											</ScheduleFutureTab__ItemCell>
											<ScheduleFutureTab__ItemCell>
												<ScheduleFutureTab__CourtName>
													<div>
														{team.next_match?.week_day}
														{team.next_match?.start_time_string &&
															`, ${team.next_match.start_time_string}`}
													</div>
													<div>{team.next_match?.court}</div>
												</ScheduleFutureTab__CourtName>
											</ScheduleFutureTab__ItemCell>
										</ScheduleFutureTab_Item>
									)}
									{team.next_ref?.match_id && (
										<ScheduleFutureTab_Item
											onClick={() => {
												team.next_ref?.external?.pool_bracket_info &&
													openPoolBracketModal(
														team.next_ref.external.pool_bracket_info,
														String(team.team_id),
													);
											}}
										>
											<ScheduleFutureTab__ItemCell $isBracket>
												{!isBracket && <strong>{addOrdinalSuffix(index + 1)}</strong>}
												{isBracket && index && <strong>Loser</strong>}
											</ScheduleFutureTab__ItemCell>
											<ScheduleFutureTab__ItemCell>
												<div>
													<i>
														{team.team_name} Works {team.next_ref.display_name}
													</i>
												</div>
											</ScheduleFutureTab__ItemCell>
											<ScheduleFutureTab__ItemCell>
												<div>
													<span>
														{team.next_ref.week_day}
														{team.next_ref.start_time_string &&
															`, ${team.next_ref.start_time_string}`}
													</span>
												</div>
												<div>{team.next_ref.court}</div>
											</ScheduleFutureTab__ItemCell>
										</ScheduleFutureTab_Item>
									)}
								</Fragment>
							);
						})}
					</ScheduleFutureTab__List>
				</FutureTab__Container>
			</Tab__Wrapper>
		</>
	);
};

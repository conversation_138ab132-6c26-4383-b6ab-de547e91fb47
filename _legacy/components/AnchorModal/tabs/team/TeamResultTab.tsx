import { useBracketModal } from '@/shared/hooks/useBracketModal';
import { usePoolModal } from '@/shared/hooks/usePoolModal';
import arrowRight from '@assets/arrowRightBlue-icon.svg';
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { StringParam, useQueryParams } from 'use-query-params';

import { Loader } from '@/components/Loader';
import { ModalTitle } from '@/components/ModalTitle';

import { DivisionPool, TeamSingleResults } from '@/generated/graphql';

import { PoolBracketUUID } from '../../types';
import {
	ResultTab__Container,
	ResultTab__Item,
	ResultTab__List,
	Tab__NextModalLink,
	Tab__Wrapper,
} from '../styled';

type PropsT = {
	openPoolModal: (_data: PoolBracketUUID) => void;
	resultList: TeamSingleResults[];
	currentTeamId?: number | null;
	resultsOfRound?: TeamSingleResults[] | null;
	poolOfTeam: (DivisionPool & { division_id?: string }) | null;
};

export const TeamResultTab = ({
	// openPoolModal,
	resultList,
	currentTeamId,
	poolOfTeam,
	resultsOfRound = [],
}: PropsT) => {
	const getResult = (roundId: string) => {
		const currentRound = resultsOfRound?.find((result) => result.uuid === roundId);
		if (currentRound) {
			const currentStats = currentRound.pb_stats?.find(
				(item) => Number(item.team_id) === currentTeamId,
			);
			return currentStats || null;
		}
		return null;
	};
	const isPool = (roundId: string) => {
		return !!resultsOfRound?.find((result) => result.uuid === roundId)?.is_pool;
	};

	const { pathname } = useLocation();

	const [queryParams, setQueryParams] = useQueryParams({
		divisionId: StringParam,
	});

	useEffect(() => {
		if (pathname.includes('clubs-teams')) {
			setQueryParams({ divisionId: poolOfTeam?.division_id });
		}
	}, [pathname, poolOfTeam, queryParams, setQueryParams]);

	const { poolModalElement, openPoolModal, poolDataLoading } = usePoolModal();

	const { bracketModalElement, openBracketModal, bracketDataLoading } =
		useBracketModal(openPoolModal);

	const loading = poolDataLoading || bracketDataLoading;

	return (
		<>
			{loading && <Loader />}
			{bracketModalElement}
			{poolModalElement}
			<Tab__Wrapper>
				<ResultTab__Container>
					<ModalTitle title="Results" />
					{resultList.map((result) => {
						const resultOfCurrentRound = getResult(result.uuid!);
						return (
							<div key={`anchor_${result.uuid}`}>
								<Tab__NextModalLink
									onClick={() => {
										isPool(result.uuid!)
											? openPoolModal(String(currentTeamId), result.uuid!)
											: openBracketModal({
													team_id: String(currentTeamId),
													bracketId: result.uuid!,
													division_id: poolOfTeam?.division_id || '',
												});
									}}
								>
									<span>
										{result.round_name} {result.pb_name}
									</span>
									{resultOfCurrentRound && isPool(result.uuid!) && (
										<span>
											{resultOfCurrentRound.matches_won}-{resultOfCurrentRound.matches_lost},{' '}
											{resultOfCurrentRound.sets_won}-{resultOfCurrentRound.sets_lost},{' '}
											{Math.round(Number(resultOfCurrentRound.sets_pct))}%
										</span>
									)}
									<img src={arrowRight} alt="" />
								</Tab__NextModalLink>
								<ResultTab__List>
									{result.matches
										.filter((m) => m.results)
										.map(({ opponent_team_id, opponent_team_name, results, match_id }) => {
											const sets = [results?.set1, results?.set2, results?.set3].filter(Boolean);
											const scores = [results?.team1.sets_won, results?.team1.sets_lost];
											if (opponent_team_id === results?.team1.roster_team_id) {
												scores.reverse();
											}
											return (
												<ResultTab__Item key={match_id}>
													<p>
														<strong>{scores[0]! > scores[1]! ? 'W ' : 'L '}</strong>
														<span>vs {opponent_team_name}</span>
													</p>
													<p>
														<strong>
															{scores[0]}-{scores[1]}
														</strong>
													</p>
													<p>
														<span>{sets.join(', ')}</span>
													</p>
												</ResultTab__Item>
											);
										})}
								</ResultTab__List>
							</div>
						);
					})}
				</ResultTab__Container>
			</Tab__Wrapper>
		</>
	);
};

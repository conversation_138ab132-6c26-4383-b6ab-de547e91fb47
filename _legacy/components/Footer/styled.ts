import styled from 'styled-components';

export const Footer__Wrapper = styled.footer`
	height: 56px;
	background: #f9fafb;
	width: 100%;
	position: fixed;
	bottom: 0;
	z-index: 99;
`;
export const Footer__Inner = styled.div`
	width: calc(100% - 120px);
	margin: auto;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 100%;
`;
export const Footer__Copyright = styled.p`
	color: #919eab;
	font-size: 14px;
	line-height: 22px;
`;
export const Footer__Nav = styled.ul`
	display: flex;
	gap: 28px;
`;
export const Footer__NavItem = styled.li`
	color: #919eab;
	font-size: 14px;
	line-height: 22px;
	position: relative;
	&:last-child {
		padding-right: 0;
	}
	a {
		color: #919eab;
		text-decoration: none;
		&:hover {
			text-decoration: underline;
		}
	}
`;

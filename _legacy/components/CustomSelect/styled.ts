import styled from 'styled-components';

export const CustomSelectWrapper = styled.div<{ $width?: number; $isBlue?: boolean }>`
	min-width: ${({ $width }) => ($width ? `${$width}px` : '100%')};
	width: 100%;
	border-radius: 4px;
	border: 1px solid ${({ $isBlue, theme }) => ($isBlue ? `${theme.colors.blue}` : '#f9fafb')};
	/* padding: 7px 18px 7px 8px; */
	position: relative;
	span {
		color: ${({ $isBlue, theme }) => ($isBlue ? `${theme.colors.blue}` : '#f9fafb')};
		font-size: 12px;
		position: absolute;
		left: 8px;
		top: 50%;
		transform: translateY(-50%);
		line-height: 18px;
		pointer-events: none;
		width: calc(100% - 25px);
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			font-size: 14px;
			font-weight: 400;
			line-height: 22px;
		}
	}
	select {
		position: relative;
		z-index: 2;
		height: 100%;
		/* color: ${({ $isBlue, theme }) => ($isBlue ? `${theme.colors.blue}` : '#f9fafb')}; */
	}
	img {
		position: absolute;
		right: 5px;
		top: 50%;
		transform: translateY(-50%);
	}
`;

export const CustomSelect__Select = styled.select`
	border: none;
	opacity: 0;
	width: 100%;
	background: transparent;
	font-size: 12px;
	line-height: 18px;
	-moz-appearance: none;
	-webkit-appearance: none;
	appearance: none;
	padding: 5px;
	&:focus {
		outline: none;
	}
`;

import Providers from '@/app/Providers';
import type { Meta, StoryObj } from '@storybook/react';

import { Box } from '@mui/material';

import { Loader } from '.';

const meta = {
	title: 'Loader',
	component: Loader,
	parameters: {
		layout: 'padded',
		docs: {
			description: {
				component: 'Default loader component with overlay',
			},
		},
	},
	tags: ['autodocs'],
	argTypes: {
		color: {
			defaultValue: 'primary',
			options: ['primary', 'secondary', 'error', 'info', 'success', 'warning', 'inherit'],
			control: { type: 'select' },
		},
	},

	decorators: [
		(Story) => {
			return (
				<Providers>
					<Box sx={{ height: '600px' }}>
						<Story />
					</Box>
				</Providers>
			);
		},
	],
} satisfies Meta<typeof Loader>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		color: 'primary',
	},
};

import styled from 'styled-components';

export const StyledSpinnerWrapper = styled.div`
	width: 100px;
	text-align: center;
	position: fixed;
	bottom: 80px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 1000;
`;
export const StyledSpinner = styled.div`
	width: 30px;
	height: 30px;
	border: 5px solid #dfe3e8;
	border-radius: 50%;
	display: inline-block;
	position: relative;
	box-sizing: border-box;
	animation: rotation 1s linear infinite;
	&::after {
		content: '';
		box-sizing: border-box;
		position: absolute;
		left: 50%;
		top: calc(50% - 1px);
		transform: translate(-50%, -50%);
		width: 30px;
		height: 30px;
		border-radius: 50%;
		border: 3px solid transparent;
		border-bottom-color: #f9fafb;
	}

	@keyframes rotation {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
`;
export const StyledSpinnerText = styled.p`
	color: #637381;
	font-size: 10px;
	font-weight: 500;
	line-height: 16px;
`;

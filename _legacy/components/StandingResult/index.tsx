import { addOrdinalSuffix } from '@/utils';
import { DivisionStanding } from '@generated/graphql';

type Standing = Pick<
	DivisionStanding,
	'matches_won' | 'matches_lost' | 'sets_won' | 'sets_lost' | 'rank'
>;

type Props = {
	standing?: Standing | null;
};

export const StandingResult = ({ standing }: Props) => {
	const { matches_won, matches_lost, sets_won, sets_lost, rank } = standing || {};
	return `${matches_won || 0}-${matches_lost || 0}(${sets_won || 0}-${sets_lost || 0}) ${addOrdinalSuffix(rank || 0)}`;
};

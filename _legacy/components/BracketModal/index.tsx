import { useBlockScroll } from '@/shared/hooks/useBlockScroll';
import { bracketGridMatchNameSignal } from '@/signals/bracketGridMatchNameSignal';
import closeIcon from '@assets/close-black-icon.svg';
import { useEffect, useLayoutEffect } from 'react';
import { createPortal } from 'react-dom';

import { DivisionPool } from '@/generated/graphql';

import { Loader } from '../Loader';
import { BracketSingle } from './components/BracketSingle';
import { useBracket } from './hooks/useBracket';
import {
	BracketModal__Header,
	BracketModal__HeaderCloseBtn,
	BracketModal__HeaderTitle,
	BracketModal__Main,
	BracketModal__Wrapper,
} from './styled';

type PropsT = {
	close: (_isButtonClosed?: boolean) => void;
	poolId: string;
	bracketRounds: DivisionPool[];
	poolOfTeam: DivisionPool;
	isShowOverlay?: boolean;
};

export const BracketModal = ({
	close,
	poolId,
	bracketRounds,
	poolOfTeam,
	isShowOverlay = true,
}: PropsT) => {
	useBlockScroll();
	const { loading, bracketTemplate, setCurrentPoolId, currentPoolId } = useBracket({
		poolId,
	});

	useEffect(() => {
		return () => {
			bracketGridMatchNameSignal.value = '';
		};
	}, []);

	const currentRound = bracketRounds.find((r) => r.uuid === currentPoolId)!;

	const mount = document.getElementById('portal')!;
	const el = document.createElement('div');
	el.setAttribute('id', 'modal');

	const overlay = document.createElement('div');
	isShowOverlay && overlay.setAttribute('id', 'overlay');
	isShowOverlay && overlay.addEventListener('click', () => close());
	useLayoutEffect(() => {
		mount.appendChild(el);
		isShowOverlay && document.body.appendChild(overlay);
		return () => {
			mount.removeChild(el);
			isShowOverlay && document.body.removeChild(overlay);
			isShowOverlay && overlay.removeEventListener('click', () => close);
		};
	}, [close, el, isShowOverlay, mount, overlay]);

	useEffect(() => {
		return () => {
			close();
		};
	}, [close]);

	return createPortal(
		<>
			<BracketModal__Wrapper className="bracket-scroll-container">
				{loading && <Loader />}
				<BracketModal__Header>
					<BracketModal__HeaderTitle>
						{currentRound?.pb_name || poolOfTeam.display_name}
					</BracketModal__HeaderTitle>
					<BracketModal__HeaderCloseBtn onClick={() => close(true)}>
						<img src={closeIcon} alt="" />
					</BracketModal__HeaderCloseBtn>
				</BracketModal__Header>
				<BracketModal__Main>
					{bracketTemplate && (
						<BracketSingle
							close={close}
							bracketTemplate={bracketTemplate}
							currentRound={currentRound}
							bracketRounds={bracketRounds}
							setCurrentPoolId={setCurrentPoolId}
						/>
					)}
				</BracketModal__Main>
			</BracketModal__Wrapper>
		</>,
		el as HTMLElement,
	);
};

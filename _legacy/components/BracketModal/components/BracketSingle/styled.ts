import { css, styled } from 'styled-components';

export const BracketSingle__WrapperStyled = styled.div<{ $isZoomUp: boolean }>`
	display: flex;
	/* //* This is a hidden input for correct working of zoom */
	transform-origin: top left !important;
	width: auto;
	max-height: calc(100vh - 100px);
	max-height: ${(props) => (props.$isZoomUp ? '100%' : 'calc(100vh - 100px)')};
	padding-top: 120px;

	@media screen and (max-width: ${(props) => props.theme.breakpoints.medium}) {
		padding-top: ${(props) => (props.$isZoomUp ? '410px' : '120px')};
	}
`;
export const BracketSingle__ZoomButton = styled.button`
	border-radius: 50%;
	background-color: #212b36;
	box-shadow: 0px 20px 40px -4px rgba(145, 158, 171, 0.16);
	height: 32px;
	width: 32px;
	position: fixed;
	right: 24px;
	bottom: 24px;
	z-index: 9;
	cursor: pointer;
	border: none;
	display: flex;
	justify-content: center;
	align-items: center;
	transition: background-color 300ms;
	outline: none;
`;

const commonNavigateBtnStyles = css`
	border: none;
	position: relative;
	border-radius: 50%;
	background-color: #fff;
	box-shadow: 0px 12px 24px -4px rgba(0, 0, 0, 0.16);
	width: 32px;
	height: 32px;
	position: fixed;
	cursor: pointer;
	outline: none;
	top: 120px;
	z-index: 9;
`;
export const BracketSingle__NavigatePrev = styled.button`
	${commonNavigateBtnStyles}
	left: 5px;
	cursor: pointer;
`;
export const BracketSingle__NavigateNext = styled.button`
	${commonNavigateBtnStyles}
	right: 5px;
`;
export const BracketSingle__ColumnWrapper = styled.div<{ $isScrollableColumns: boolean }>`
	display: ${(props) => (props.$isScrollableColumns ? 'block' : 'none')};
	height: 55px;
	display: none;
	width: 100%;
	background: #fff;
	position: relative;
	z-index: 9;
`;
export const BracketSingle__ColumnContainer = styled.div`
	overflow: auto;
	display: flex;
	scrollbar-width: none;
	&::-webkit-scrollbar {
		display: none;
	}
`;

import { styled } from 'styled-components';

import { MATCH_HEIGHT } from '../../config';

export const Match__WrapperStyled = styled.div`
	height: ${MATCH_HEIGHT / 2}px;
`;

export const Match__ContainerStyled = styled.div`
	padding: 0 10px;
`;

export const Match__SingleWrapperStyled = styled.div`
	position: relative;
	height: ${MATCH_HEIGHT}px;
	display: flex;
	align-items: center;
	z-index: 3;
	background: #fff;
`;
export const Match__SingleNumberStyled = styled.span<{ $isActive: boolean }>`
	font-size: 10px;
	color: #637381;
	transform: rotate(-90deg);
	background: #fff;
	padding: 0px 3px;
	border-radius: 3px;
	border: 1px solid
		${(props) => (props.$isActive ? (props) => props.theme.colors.blue : 'transparent')} !important;
`;

export const Match__SingleParticipantWrapperStyled = styled.div<{ $isOpacity: boolean }>`
	width: 100%;
	position: relative;
	z-index: 9;
	width: 250px;
	opacity: ${(props) => (props.$isOpacity ? 0.3 : 1)};
`;

export const Match__SingleParticipantStyled = styled.div<{
	$isActive: boolean;
	$isWinner: boolean;
}>`
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	background: ${(props) => (!props.$isActive ? '#f4f6f8' : '#000')} !important;
	color: ${(props) =>
		!props.$isWinner ? (props) => props.theme.colors.blue : '#454f5b'} !important;
	color: ${(props) => props.$isActive && '#fff'} !important;
	border: 1px solid #e6e5e5;
	font-size: 12px;
	line-height: 18px;
	padding: 0 0 0 5px;
	&:first-child {
		border-bottom: 1px solid #fff;
		border-top-left-radius: 5px;
		border-top-right-radius: 5px;
		border-bottom: 1px solid #e6e5e5;
	}
	&:last-child {
		border-top: 1px solid #fff;
		border-bottom-left-radius: 5px;
		border-bottom-right-radius: 5px;
	}
`;
export const Match__SingleParticipantName = styled.div<{ $isQualified: boolean }>`
	width: 160px;
	padding: ${(props) => (props.$isQualified ? '5px 30px 5px 0' : '5px 0')};
	position: relative;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	cursor: pointer;
`;

export const Match__PreviouslyQualifiedWrapper = styled.section<{
	$isSet3Exists: boolean;
	$isFirstTeam: boolean;
}>`
	position: absolute;
	left: ${(props) => (props.$isSet3Exists ? '125px' : '145px')};
	top: ${(props) => (props.$isFirstTeam ? '5px' : '35px')};
	z-index: 2;
`;

export const Match__SingleDescriptionStyled = styled.div`
	font-size: 13px;
	color: 'black';
	position: absolute;
	top: -10px;
`;
export const Match__Scores = styled.ul<{
	$isWinner: boolean;
	$isActive: boolean;
}>`
	display: flex;
	li {
		color: ${(props) =>
			!props.$isWinner ? (props) => props.theme.colors.blue : '#454f5b'} !important;
		color: ${(props) => props.$isActive && '#fff'} !important;
		width: 25px;
		border-left: 1px solid #e6e5e5;
		padding: 5px;
		text-align: center;
		b {
			color: ${(props) =>
				!props.$isWinner ? (props) => props.theme.colors.blue : '#454f5b'} !important;
			color: ${(props) => props.$isActive && '#fff'} !important;
		}
	}
`;
export const Match__HeaderExtraData = styled.div`
	position: absolute;
	top: 6px;
	right: 8px;
	span {
		color: #c4cdd5;
		font-size: 10px;
		margin: 0 0 0 8px;
		&:first-child {
			margin: 0;
		}
	}
`;
export const Match__FooterExtraData = styled.div`
	position: absolute;
	bottom: 10px;
	left: 22px;
	span {
		color: #c4cdd5;
		font-size: 10px;
		margin: 0 0 0 8px;
		&:first-child {
			margin: 0;
		}
	}
`;

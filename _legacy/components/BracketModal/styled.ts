import styled from 'styled-components';

export const BracketModal__Wrapper = styled.div`
	position: fixed;
	width: 100%;
	height: 100%;
	background: #fff;
	top: 0;
	left: 0;
	top: 0;
	overflow: scroll;
`;
export const BracketModal__Header = styled.header`
	position: fixed;
	height: 56px;
	width: 100%;
	padding: 16px;
	z-index: 999;
	background: #fff;
	z-index: 999;
`;
export const BracketModal__HeaderCloseBtn = styled.button`
	position: absolute;
	right: 16px;
	top: 16px;
	border: none;
	background: transparent;
	cursor: pointer;
`;
export const BracketModal__HeaderTitle = styled.p`
	font-size: 16px;
	line-height: 24px;
	font-weight: 700;
	text-align: center;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	padding: 0 50px 0 0;
`;
export const BracketModal__Main = styled.main`
	/* width: fit-content; */
	max-height: calc(100vh - 100px);
`;

import { RefObject, useEffect, useMemo, useRef, useState } from 'react';

import { alignToStep, convertTimeFormat, getIntervalsInRange } from '@/utils/time';

type TimeSlot = {
	time24h: string;
	displayTime: string;
};

export type HookProps = {
	timeStep?: number;
	displayFormat?: string;
	timeRangeMinMax: [string, string];
	selectedTimeRange: [string, string] | null;
	onSelectTimeRange: (_timeRange: [string, string]) => void;
};

const COUNT_OF_DISABLED_ITEMS = 2;

export const useTimeRange = ({
	timeStep = 60,
	displayFormat = 'h a',
	timeRangeMinMax: [minTime, maxTime],
	selectedTimeRange,
	onSelectTimeRange,
}: HookProps) => {
	const timeRangeStartRef = useRef<HTMLUListElement>(null);
	const timeRangeEndRef = useRef<HTMLUListElement>(null);
	const dropdownRef = useRef<HTMLDivElement>(null);
	const [isOpen, setIsOpen] = useState(false);
	const [selectedStartTime, selectedEndTime] = selectedTimeRange || [];
	const [startTime, setStartTime] = useState(selectedStartTime);
	const [endTime, setEndTime] = useState(selectedEndTime);

	// Calculating time options and displaying time range
	const [timeSlots, minSelectableTime, maxSelectableTime, startRangeMaxTime] = useMemo(() => {
		const alignedMinTime = alignToStep(minTime, timeStep, true, 'HH:mm');
		const alignedMaxTime = alignToStep(maxTime, timeStep, false, 'HH:mm');
		const timesList = getIntervalsInRange('00:00', '23:59', timeStep, 'HH:mm');
		// Adding extra options roundedMaxTime the start and end of the time range
		let minTimeListIndex = timesList.indexOf(alignedMinTime) - COUNT_OF_DISABLED_ITEMS;
		let maxTimeListIndex = timesList.indexOf(alignedMaxTime) + COUNT_OF_DISABLED_ITEMS;
		if (minTimeListIndex < 0) minTimeListIndex = 0;
		if (maxTimeListIndex < 0) maxTimeListIndex = 0;
		const rangedOptions = timesList.slice(minTimeListIndex, maxTimeListIndex + 1);
		const startRangeMaxTime = rangedOptions[rangedOptions.length - (COUNT_OF_DISABLED_ITEMS + 1)];

		return [
			rangedOptions.map<TimeSlot>((time) => ({
				time24h: time,
				displayTime: convertTimeFormat(time, 'HH:mm', displayFormat),
			})),
			alignedMinTime,
			alignedMaxTime,
			startRangeMaxTime,
		];
	}, [minTime, maxTime, timeStep, displayFormat]);

	useEffect(() => {
		setStartTime(selectedStartTime);
	}, [selectedStartTime]);

	useEffect(() => {
		setEndTime(selectedEndTime);
	}, [selectedEndTime]);

	useEffect(() => {
		if (!isOpen && (!startTime || !endTime)) {
			setStartTime(selectedStartTime);
			setEndTime(selectedEndTime);
		}
	}, [endTime, isOpen, startTime, selectedStartTime, selectedEndTime]);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
				setIsOpen(false);
			}
		};
		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [isOpen]);

	useEffect(() => {
		const scrollToTime = (time24h: string, ref: RefObject<HTMLUListElement>) => {
			if (!ref.current) {
				return;
			}
			const targetElement = ref.current.querySelector(`[data-time24h="${time24h}"]`);
			if (targetElement) {
				targetElement.scrollIntoView({
					behavior: 'smooth',
					block: 'center',
				});
			}
		};

		if (isOpen) {
			document.body.classList.add('block-scroll');
			startTime && scrollToTime(startTime, timeRangeStartRef);
			endTime && scrollToTime(endTime, timeRangeEndRef);
		} else {
			document.body.classList.remove('block-scroll');
		}
	}, [startTime, endTime, isOpen]);

	const [displaySelectedStartTime, displaySelectedEndTime] = useMemo<[string, string]>(() => {
		if (!selectedStartTime || !selectedEndTime) {
			return ['', ''] as [string, string];
		}
		return [
			convertTimeFormat(selectedStartTime, 'HH:mm', displayFormat),
			convertTimeFormat(selectedEndTime, 'HH:mm', displayFormat),
		];
	}, [selectedStartTime, selectedEndTime, displayFormat]);

	const [displayStartTime, displayEndTime] = useMemo<[string, string]>(() => {
		return [
			startTime ? convertTimeFormat(startTime, 'HH:mm', displayFormat) : '',
			endTime ? convertTimeFormat(endTime, 'HH:mm', displayFormat) : '',
		];
	}, [startTime, endTime, displayFormat]);

	const clear = () => {
		setStartTime(timeSlots[COUNT_OF_DISABLED_ITEMS].time24h);
		setEndTime(timeSlots[COUNT_OF_DISABLED_ITEMS + 1].time24h);
	};

	const apply = () => {
		if (startTime && endTime) {
			setIsOpen(false);
			onSelectTimeRange([startTime, endTime]);
		}
	};

	return {
		isOpen,
		setIsOpen,
		startTime,
		setStartTime,
		endTime,
		setEndTime,
		clear,
		apply,
		timeSlots,
		minSelectableTime,
		maxSelectableTime,
		startRangeMaxTime,
		dropdownRef,
		timeRangeStartRef,
		timeRangeEndRef,
		displaySelectedStartTime,
		displaySelectedEndTime,
		displayStartTime,
		displayEndTime,
	};
};

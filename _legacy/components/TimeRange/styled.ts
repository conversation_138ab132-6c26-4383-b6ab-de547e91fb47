import styled, { css } from 'styled-components';

export const TimeRange__Wrapper = styled.div`
	position: relative;
	height: 100%;
`;

export const TimeRange__Select = styled.div<{ $isOpen?: boolean }>`
	border-radius: 4px;
	border: 1px solid #f9fafb;
	font-size: 14px;
	line-height: 18px;
	color: #f9fafb;
	padding: 12px 8px;
	width: 100%;
	position: relative;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	padding-right: 25px;
	cursor: pointer;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 12px;
	}
	img {
		position: absolute;
		right: 8px;
		top: 50%;
		transition: transform 300ms;
		transform: translateY(-50%);
	}
`;
export const TimeRange__Content = styled.div<{ $isOpen?: boolean }>`
	width: 276px;
	height: 438px;
	background: #fff;
	padding: 22px 16px 16px 16px;
	border-radius: 10px;
	box-shadow: 0px 8px 16px 0px rgba(145, 158, 171, 0.16);
	position: absolute;
	top: 52px;
	left: 50%;
	transform: translateX(-50%);
	display: ${({ $isOpen }) => ($isOpen ? 'block' : 'none')};
	z-index: 2;
`;
export const TimeRange__Header = styled.div`
	font-size: 14px;
	line-height: 22px;
	font-weight: 700;
	color: #212b36;
	text-align: center;
	margin: 0 0 16px;
`;
export const TimeRange__Main = styled.div`
	width: 100%;
	display: flex;
	justify-content: space-between;
`;
export const TimeRange__Box = styled.div`
	width: 115px;
`;
export const TimeRange__SelectedField = styled.input<{ $isEmpty?: boolean }>`
	border-radius: 4px;
	background-color: #fff;
	box-shadow: 0px 20px 40px -4px rgba(145, 158, 171, 0.16);
	border: ${({ $isEmpty }) => ($isEmpty ? '1px solid #FF4842' : '0.5px solid #d9d9d9')};
	color: ${({ $isEmpty }) => ($isEmpty ? '#FF4842' : '#212b36')};
	width: 100%;
	height: 40px;
	padding: 0px 10px;
	font-size: 14px;
	margin: 0 0 16px;
	text-align: center;
`;
export const TimeRange__List = styled.ul`
	border-radius: 4px;
	background: #fff;
	border: 0.5px solid #d9d9d9;
	height: 232px;
	overflow-y: scroll;
	margin: 0 0 8px;
	padding: 0 8px;
`;
const commonTimeRangeItemStyles = css`
	height: 32px;
	margin: 0 0 17px;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 14px;
	font-weight: 600;
	text-align: center;
	cursor: pointer;
	&:last-child {
		margin: 0;
	}
	&:hover {
		background: #f4f6f8;
	}
`;
export const TimeRange__ItemStart = styled.li<{ $isSelected?: boolean; $isDisabled: boolean }>`
	${commonTimeRangeItemStyles}
	background: ${({ $isSelected }) => ($isSelected ? '#f4f6f8' : 'transparent')};
	color: ${({ $isSelected }) => ($isSelected ? '#212B36' : '#637381')};
	opacity: ${({ $isDisabled }) => ($isDisabled ? 0.1 : 1)};
	pointer-events: ${({ $isDisabled }) => ($isDisabled ? 'none' : 'auto')};
`;
export const TimeRange__ItemEnd = styled.li<{ $isSelected?: boolean; $isDisabled: boolean }>`
	${commonTimeRangeItemStyles}
	background: ${({ $isSelected }) => ($isSelected ? '#f4f6f8' : 'transparent')};
	opacity: ${({ $isDisabled }) => ($isDisabled ? 0.1 : 1)};
	pointer-events: ${({ $isDisabled }) => ($isDisabled ? 'none' : 'auto')};
	color: ${({ $isSelected }) => ($isSelected ? '#212B36' : '#637381')};
`;

export const TimeRange__Footer = styled.footer`
	display: flex;
	border-top: 0.5px solid #dfe3e8;
	margin-top: 16px;
	padding-top: 16px;
	gap: 14px;
	button {
		font-weight: normal;
	}
`;
export const TimeRange__Button = styled.button<{
	$variant?: 'contained' | 'outlined';
	$isDisabled?: boolean;
}>`
	background: ${(props) =>
		props.$variant === 'contained' ? props.theme.colors.blue : 'rgba(145, 158, 171, 0.08)'};
	color: ${(props) => (props.$variant === 'contained' ? '#fff' : '#637381')};
	border: none;
	width: 100%;
	border-radius: 2px;
	height: 36px;
	padding: 6px 16px;
	text-align: center;
	line-height: 18px;
	font-weight: 400;
	font-size: 12px;
	cursor: pointer;
	opacity: ${(props) => (props.$isDisabled ? 0.3 : 1)};
`;

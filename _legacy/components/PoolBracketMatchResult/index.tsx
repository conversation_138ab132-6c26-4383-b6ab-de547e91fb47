import { PoolOrBracketStatItem } from '@generated/graphql';

type PoolOrBracketStat = Pick<PoolOrBracketStatItem, 'matches_won' | 'matches_lost'>;

type Props = {
	pbStat?: PoolOrBracketStat | null;
};

export const PoolBracketMatchResult = ({ pbStat }: Props) => {
	if (!pbStat) return null;
	if ('matches_won' in pbStat && 'matches_lost' in pbStat) {
		return `${pbStat.matches_won}-${pbStat.matches_lost}`;
	}
	return null;
};

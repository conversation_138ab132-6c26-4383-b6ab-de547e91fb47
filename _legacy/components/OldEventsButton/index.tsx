import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useParams } from 'react-router-dom';

import navLinkIcon from './img/navLinkIcon.svg';
import { StyledOldEventsButtonWrapper } from './styled';

export const OldEventsButton = () => {
	const HOST =
		import.meta.env.VITE_APP_ENV === 'development'
			? import.meta.env.VITE_DEV_EVENTS_SERVER
			: import.meta.env.VITE_PROD_EVENTS_SERVER;

	const { breakPont } = useCurrentSize();
	const params = useParams();
	const openOldEventsPage = () => {
		if (params.id) {
			window.open(`${HOST}/#/events/${params.id}`, '_blank');
		} else {
			window.open(`${HOST}/#/events`, '_blank');
		}
	};
	return (
		<StyledOldEventsButtonWrapper onClick={openOldEventsPage} $isMobile={breakPont === 'small'}>
			<img src={navLinkIcon} alt="link icon" />
			{breakPont === 'small' ? 'Old View' : 'Old Events Page'}
		</StyledOldEventsButtonWrapper>
	);
};

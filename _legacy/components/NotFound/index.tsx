import outFavoriteSmallIcon from '@assets/outFavoriteSmall-icon.svg';
import searchBlueIcon from '@assets/searchBlueIcon.svg';
import { useNavigate, useParams } from 'react-router-dom';

import {
	NotFound__Button,
	NotFound__Message,
	NotFound__SearchIcon,
	NotFound__Wrapper,
} from './styled';

type PropsT = {
	type:
		| 'favorites'
		| 'clubs-teams'
		| 'court-grid'
		| 'court-grid-empty'
		| 'all-events'
		| 'sportwrench'
		| 'roster'
		| 'divisions'
		| 'pool-brackets'
		| 'qualified';
	onClick?: () => void;
};
export const NotFound = ({ type, onClick }: PropsT) => {
	const navigate = useNavigate();
	const params = useParams();

	let message = null;
	let buttonLabel = null;
	const clickHandler = () => {
		if (onClick) {
			return onClick();
		}
		if (type === 'favorites') {
			return navigate(`/events/${params.id}/clubs-teams`);
		}
		if (type === 'sportwrench') {
			return navigate(`/events/${params.id}/about`);
		}
	};
	if (type === 'favorites') {
		message = (
			<NotFound__Message
				dangerouslySetInnerHTML={{
					__html: `No Favorites. Search Clubs & Teams and <br /> tap <img src="${outFavoriteSmallIcon}" alt="" /> to create a favorite.`,
				}}
			></NotFound__Message>
		);
		buttonLabel = 'Clubs & Teams page';
	}
	if (type === 'all-events' || type === 'pool-brackets') {
		message = (
			<NotFound__Message>
				<NotFound__SearchIcon src={searchBlueIcon} alt="" />
				No results found for this search. Please modify your criteria and try again. Click Reset for
				a new search.
			</NotFound__Message>
		);
		buttonLabel = null;
	}
	if (type === 'roster' || type === 'divisions') {
		message = (
			<NotFound__Message data-testid="not-found">
				<NotFound__SearchIcon src={searchBlueIcon} alt="" />
				No results found for this search. Please modify your criteria and try again. Click Reset for
				a new search.
			</NotFound__Message>
		);
		buttonLabel = null;
	}
	if (type === 'clubs-teams' || type === 'qualified') {
		message = (
			<NotFound__Message data-testid="not-found">
				<NotFound__SearchIcon src={searchBlueIcon} alt="" />
				No results found for this search. Please modify your criteria and try again. Click Reset for
				a new search.
			</NotFound__Message>
		);
		buttonLabel = null;
	}
	if (type === 'court-grid') {
		message = (
			<NotFound__Message>
				<NotFound__SearchIcon src={searchBlueIcon} alt="" />
				No matches found based on the <br /> selected filters. <br /> Click Reset for a new search.
			</NotFound__Message>
		);
		buttonLabel = 'Reset';
	}
	if (type === 'court-grid-empty') {
		message = (
			<NotFound__Message>
				<NotFound__SearchIcon src={searchBlueIcon} alt="" />
				No matches found
			</NotFound__Message>
		);
		buttonLabel = null;
	}
	if (type === 'sportwrench') {
		message = (
			<NotFound__Message>
				No results found for this search. Please modify your criteria and try again. Click Reset for
				a new search.
			</NotFound__Message>
		);
		buttonLabel = 'Back to Event page';
	}
	return (
		<NotFound__Wrapper>
			{message}
			{buttonLabel && <NotFound__Button onClick={clickHandler}>{buttonLabel}</NotFound__Button>}
		</NotFound__Wrapper>
	);
};

import styled from 'styled-components';

export const NotFound__Wrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	padding-top: 220px;
	position: relative;
`;
export const NotFound__Message = styled.div`
	color: #637381;
	text-align: center;
	font-size: 16px;
	line-height: 24px;
	margin: auto;
	margin: 0 0 32px;
	position: relative;
	width: 550px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		width: 350px;
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 30px;
	}
`;
export const NotFound__SearchIcon = styled.img`
	position: absolute;
	top: -100px;
	left: 50%;
	transform: translate(-50%, 0);
`;
export const NotFound__Button = styled.button`
	color: #f9fafb;
	font-size: 14px;
	line-height: 22px;
	border-radius: 6px;
	border: none;
	padding: 8px 36px;
	min-width: 200px;
	background: ${(props) => props.theme.colors.blue};
	border: none;
	cursor: pointer;
	margin: 0 auto;
`;

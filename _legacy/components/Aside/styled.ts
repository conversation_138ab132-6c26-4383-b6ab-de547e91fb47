import styled from 'styled-components';

import { StyledOldEventsButtonWrapper } from '../OldEventsButton/styled';

export const Aside__IconButton = styled.button`
	width: 24px;
	height: 24px;
	border: none;
	background: transparent;
`;
export const Aside__Wrapper = styled.div`
	width: 239px;
	max-width: 239px;
	max-height: 519px;
	overflow-y: auto;
	overflow-x: hidden;
`;
export const Aside__NavList = styled.ul``;
export const Aside__NavItem = styled.li`
	font-size: 14px;
	line-height: 18px;
	padding: 16px 5px 16px 20px;
	background: #fff;
	display: flex;
	align-items: center;
	gap: 5px;
	position: relative;
	cursor: pointer;
	&:before {
		content: '';
		position: absolute;
		height: 1px;
		width: 219px;
		border-radius: 2px;
		left: 10px;
		background-color: #919eab3d;
		bottom: 1px;
	}
	&:nth-last-child(3),
	&:last-child {
		&:before {
			content: none;
		}
	}
	&:nth-last-child(2) {
		margin-top: 20px;
	}
	&:last-child {
		${StyledOldEventsButtonWrapper} {
			width: 100%;
		}
	}
	a {
		text-decoration: none;
		display: flex;
		align-items: center;
		gap: 5px;
	}
`;

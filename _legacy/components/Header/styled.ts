import { Link } from 'react-router-dom';
import styled, { css } from 'styled-components';

import { ThemeT } from '@/styles/theme';

export const Header__Wrapper = styled.header<ThemeT>`
	display: flex;
	padding-left: ${(props) => props.theme.dimensions.mobile.paddingX};
	padding-right: ${(props) => props.theme.dimensions.mobile.paddingX};
	padding-top: ${(props) => props.theme.dimensions.mobile.paddingY};
	padding-bottom: ${(props) => props.theme.dimensions.mobile.paddingY};
	align-items: center;
	height: ${(props) => props.theme.dimensions.mobile.headerHeight};
	position: fixed;
	z-index: 9;
	width: 100%;
	background: #fff;
	justify-content: space-between;

	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		height: 62px;
		padding: 20px 30px;
	}
`;
export const Header__Logo = styled(Link)``;
export const Header__Label = styled.p`
	position: relative;
	font-size: 16px;
	line-height: 24px;
	font-weight: 700;
	font-family: 'Public Sans';
	color: #637381;
	text-align: left;
	margin: auto;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		margin: 0;
	}
`;
export const Header__BackLink = styled(Link)`
	border: none;
	background: none;
	width: 24px;
	height: 24px;
	margin: 0 16px 0 0;
`;
export const Header__LogoNavBox = styled.div`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		position: static;
	}
`;
export const Header__Left = styled.div`
	display: flex;
	align-items: center;
`;
const titleStyles = css`
	color: #212b36;
	font-size: 18px;
	font-weight: 700;
	line-height: 30px;
	margin: 0 0 0 12px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: min-content;
`;
export const Header__EventName = styled.p`
	${titleStyles}
	a {
		text-decoration: none;
	}
`;
export const Header__Divider = styled.span`
	${titleStyles}
	margin: 0 8px;
`;
export const Header__PageName = styled.p`
	color: #637381;
	font-size: 18px;
	font-weight: 700;
	line-height: 30px;
	min-width: fit-content;
`;
export const Header__Right = styled.div`
	min-width: max-content;
	padding: 0 0 0 10px;
`;
export const Header__DescriptionWrapper = styled.div`
	display: flex;
	align-items: center;
	img {
		margin: 0 8px 0 0;
		&:last-child {
			margin: 0 8px 0 16px;
		}
	}
`;

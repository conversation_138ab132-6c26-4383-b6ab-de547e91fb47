import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { getTicketsDirectLink } from '@/utils';
import buyAdmissionIcon from '@assets/buy-admission-icon.svg';
import { KeyboardEvent, useEffect, useState } from 'react';
import { Link, useLocation, useParams } from 'react-router-dom';
import { useSearchParams } from 'react-router-dom';

import closeIcon from '@/assets/close-icon.svg';
import searchIcon from '@/assets/search-icon.svg';

import { Division__MainNavWrapper, Division__SearchBarAdmission } from '@/styles/shared';

import { MainNav } from '../MainNav';
import {
	SCROLL_OFFSET,
	SCROLL_WITH_DESCRIPTION_OFFSET,
	SearchBar__AdmissionTitle,
	SearchBar__EventDescription,
	SearchBar__EventInfo,
	SearchBar__EventTitle,
	SearchBar__FormWrapper,
	SearchBar__SearchIcon,
	SearchBar__Sticky,
	SearchBar__TextField,
	SearchBar__TextFieldInner,
	SearchBar__TextFieldWrapper,
	SearchBar__TitleWrapper,
	SearchBar__Wrapper,
} from './styled';

type PropsT = {
	isAssignedTickets?: boolean;
	tickets_code?: string;
	isShowBuyAdmission?: boolean;
	isTextFieldHidden?: boolean;
	eventName?: string;
	placeholder?: string;
	description?: null | (() => JSX.Element);
	search: string;
	onChangeSearch: (_val: string) => void;
	handleKeyDown?: (_event: KeyboardEvent<HTMLInputElement>) => void;
};
export const SearchBar = ({
	isShowBuyAdmission,
	isTextFieldHidden = false,
	eventName,
	description,
	search,
	onChangeSearch,
	placeholder = 'Search',
	handleKeyDown,
	isAssignedTickets,
	tickets_code,
}: PropsT) => {
	const [isFixed, setIsFixed] = useState(window.scrollY > SCROLL_OFFSET);

	const { breakPont } = useCurrentSize();
	const location = useLocation();

	const isAboutPage = location.pathname.split('/')[3] === 'about';
	const DESCRIPTION_OFFSET = isAboutPage ? SCROLL_WITH_DESCRIPTION_OFFSET : 40;
	useEffect(() => {
		const handleScroll = () => {
			const OFFSET = description ? DESCRIPTION_OFFSET : SCROLL_OFFSET;
			const newScrollTop = window.scrollY || document.documentElement.scrollTop;
			if (breakPont === 'small') {
				setIsFixed(newScrollTop > OFFSET);
			}
		};

		window.addEventListener('scroll', handleScroll);

		return () => {
			window.removeEventListener('scroll', handleScroll);
		};
	}, [DESCRIPTION_OFFSET, breakPont, description]);

	const currentPath = location.pathname.split('/')[3];

	const { isDesktopFixed } = useDesktopSticky({
		page: currentPath === 'about' ? 'about' : 'favorites',
	});

	const [, setSearchParams] = useSearchParams();

	const searchHandler = (search: string) => {
		onChangeSearch(search);
		setSearchParams({ search }, { replace: true });
	};

	useEffect(() => {
		window.scrollTo(0, 0);
		setIsFixed(false);
	}, [location.pathname]);
	const params = useParams();

	return (
		<SearchBar__Wrapper
			$isHasDescription={!!description && isAboutPage}
			$isLarge={breakPont !== 'small'}
			$isAbout={currentPath === 'about'}
			$isTextFieldHidden={isTextFieldHidden}
			className={isDesktopFixed ? 'sticky' : ''}
		>
			<SearchBar__FormWrapper>
				<SearchBar__TextFieldWrapper>
					{breakPont === 'small' && (
						<SearchBar__TitleWrapper>
							<SearchBar__EventInfo $isFullWidth={!isShowBuyAdmission}>
								{breakPont === 'small' && (
									<SearchBar__EventTitle>
										<Link to={`/events/${params.id}`}>{eventName}</Link>
									</SearchBar__EventTitle>
								)}
								{description && isAboutPage && breakPont === 'small' && (
									<SearchBar__EventDescription>{description()}</SearchBar__EventDescription>
								)}
							</SearchBar__EventInfo>
							{isShowBuyAdmission && (
								<SearchBar__AdmissionTitle
									href={getTicketsDirectLink(tickets_code!, isAssignedTickets)}
									target="_blank"
								>
									<img src={buyAdmissionIcon} alt="buy admission" /> Buy Admission
								</SearchBar__AdmissionTitle>
							)}
						</SearchBar__TitleWrapper>
					)}
					{breakPont !== 'small' && currentPath !== 'about' && (
						<Division__MainNavWrapper>
							<MainNav />
							{isShowBuyAdmission && (
								<Division__SearchBarAdmission
									href={getTicketsDirectLink(tickets_code!, isAssignedTickets)}
									target="_blank"
								>
									<img src={buyAdmissionIcon} alt="buy admission" />
									Buy admission
								</Division__SearchBarAdmission>
							)}
						</Division__MainNavWrapper>
					)}
					{!isTextFieldHidden && (
						<SearchBar__Sticky $isFixed={isFixed} $isHasDescription={!!description}>
							<SearchBar__TextFieldInner>
								{!search && <SearchBar__SearchIcon src={searchIcon} alt="search" />}
								{search && (
									<SearchBar__SearchIcon
										$isClose
										src={closeIcon}
										alt="search"
										onClick={() => searchHandler('')}
									/>
								)}
								<SearchBar__TextField
									onKeyDown={handleKeyDown}
									type="text"
									placeholder={placeholder}
									onChange={(e) => searchHandler(e.target.value)}
									value={search}
									data-testid="search-field"
								/>
							</SearchBar__TextFieldInner>
						</SearchBar__Sticky>
					)}
				</SearchBar__TextFieldWrapper>
			</SearchBar__FormWrapper>
		</SearchBar__Wrapper>
	);
};

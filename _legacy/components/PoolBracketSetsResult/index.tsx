import { PoolOrBracketStatItem } from '@generated/graphql';

import {
	PoolBracketSetsResult__Container,
	PoolBracketSetsResult__Percentage,
	PoolBracketSetsResult__Sets,
} from './styled';

type PoolOrBracketStat = Pick<PoolOrBracketStatItem, 'sets_won' | 'sets_lost' | 'sets_pct'>;

type Props = {
	pbStat?: PoolOrBracketStat | null;
};

export const PoolBracketSetsResult = ({ pbStat }: Props) => {
	if (!pbStat) return null;
	if ('sets_won' in pbStat && 'sets_lost' in pbStat && 'sets_pct' in pbStat) {
		const { sets_won, sets_lost, sets_pct } = pbStat;
		return (
			<PoolBracketSetsResult__Container>
				<PoolBracketSetsResult__Sets>
					{sets_won}-{sets_lost}
				</PoolBracketSetsResult__Sets>
				{sets_pct ? (
					<PoolBracketSetsResult__Percentage>
						{sets_pct % 1 !== 0 ? sets_pct.toFixed(2) : sets_pct}%
					</PoolBracketSetsResult__Percentage>
				) : (
					<>
						<PoolBracketSetsResult__Percentage>0%</PoolBracketSetsResult__Percentage>
					</>
				)}
			</PoolBracketSetsResult__Container>
		);
	}
	return null;
};

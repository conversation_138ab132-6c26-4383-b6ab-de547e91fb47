import styled from 'styled-components';

import { PropsT } from '.';

export const Search__Wrapper = styled.div`
	position: relative;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		max-width: 600px;
		margin: auto;
	}
`;

export const Search__Field = styled.input<{ $variant?: PropsT['variant'] }>`
	border-radius: ${(props) => (props.$variant === 'contained' ? 8 : 4)}px;
	border: 1px solid #f9fafb;
	width: 100%;
	min-height: 40px;
	background: ${(props) => (props.$variant === 'contained' ? '#fff' : 'transparent')};
	padding: 0 40px 0 16px;
	font-size: 14px;
	line-height: 22px;
	color: ${(props) => (props.$variant === 'contained' ? props.theme.colors.main : '#f9fafb')};
	outline: none;
	caret-color: #f9fafb;
	&::placeholder {
		color: ${(props) => (props.$variant === 'contained' ? '#C4CDD5' : '#f9fafb')};
	}
`;

export const Search__Icon = styled.img<{ $isClose?: boolean }>`
	position: absolute;
	right: 15px;
	top: 50%;
	transform: translateY(-50%);
	cursor: ${({ $isClose }) => ($isClose ? 'pointer' : 'auto')};
`;

import Providers from '@/app/Providers';
import type { Meta, StoryObj } from '@storybook/react';

import { ModalTitle } from '.';

const meta = {
	title: 'Titles/ModalTitle',
	component: ModalTitle,
	parameters: {
		layout: 'padded',
		docs: {
			description: {
				component: 'Title in modals with blue background',
			},
		},
	},

	tags: ['autodocs'],
	decorators: [
		(Story) => {
			return (
				<Providers>
					<Story />
				</Providers>
			);
		},
	],
} satisfies Meta<typeof ModalTitle>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		title: 'Roster',
	},
};

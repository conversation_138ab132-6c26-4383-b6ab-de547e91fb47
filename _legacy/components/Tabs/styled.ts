import styled from 'styled-components';

import { Tabs } from '@mui/material';

export const Tabs__Wrapper = styled.div<{ $withScroll?: boolean }>`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: 650px;
		margin: auto;
		overflow-y: ${({ $withScroll }) => ($withScroll ? 'auto' : 'none')};
		height: calc(100vh - 335px);
		padding-right: 20px;
		.MuiTabs-scroller {
			margin-bottom: 25px !important;
		}
	}
	.MuiTabPanel-root {
		padding: 0;
	}
`;
export const Tabs__ItemsWrapper = styled.div`
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	border-top: 1px solid #f4f6f8;
	background: #fff;
`;
export const Tabs__ItemsWrapperTabs = styled(Tabs)<{
	$isShort?: boolean;
	$isDesktopFixed?: boolean;
}>`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		position: ${(props) => props.$isDesktopFixed && 'fixed'};
		background: #fff;
		z-index: 7;
		position: fixed;
		top: 192px;
		padding: 10px 0 0;
		width: 633px;
	}
	.MuiTabs-flexContainer {
		justify-content: ${(props) => (props.$isShort ? 'center' : 'space-between')};
		gap: ${(props) => props.$isShort && '100px'};
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			justify-content: flex-start;
			gap: 24px;
			button {
				min-width: auto;
			}
		}
		button {
			padding: 0 !important;
			font-size: 14px;
			line-height: 22px;
			font-family: 'Public Sans';
			font-weight: normal;
			color: #637381;
			text-transform: capitalize;
			@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
				font-size: 16px;
				line-height: 24px;
			}
		}
	}
	.MuiTabs-indicator {
	}
	.Mui-selected {
		opacity: 1 !important;
		font-weight: 700 !important;
		color: #454f5b !important;
	}
	.MuiTab-root {
		opacity: 1 !important;
	}
`;
export const Tabs__ContentWrapper = styled.div<{ $isDesktopFixed: boolean }>`
	padding-top: ${(props) => props.$isDesktopFixed && '0'};
	.MuiBox-root {
		padding: 0;
	}
`;
export const defaultTabPanelStyles = {
	px: 0,
};
export const grayBackgroundTabPanelStyles = {
	...defaultTabPanelStyles,
	background: '#F9FAFB',
	height: '100vh',
};

import Providers from '@/app/Providers';
import type { Meta, StoryObj } from '@storybook/react';

import { Box } from '@mui/material';

import { ShadowItem } from '.';

const meta = {
	title: 'Items/ShadowItem',
	component: ShadowItem,
	parameters: {
		layout: 'padded',
		docs: {
			description: {
				component: 'Item component with shadow style that can be used as link',
			},
		},
	},
	tags: ['autodocs'],
	argTypes: {
		title: {
			defaultValue: 'Lorem',
			control: { type: 'text' },
		},
		subTitle: {
			control: { type: 'text' },
		},
		path: {
			control: { type: 'text' },
		},
	},

	decorators: [
		(Story) => {
			return (
				<Providers>
					<Box sx={{ height: '600px' }}>
						<Story />
						<br />
						<Story />
						<br />
						<Story />
					</Box>
				</Providers>
			);
		},
	],
} satisfies Meta<typeof ShadowItem>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		title: 'Lorem Ipsum',
	},
};
export const WithSubTitle: Story = {
	args: {
		title: 'Lorem Ipsum',
		subTitle: '10',
	},
};
export const WithSubTitleAndLink: Story = {
	args: {
		title: 'Lorem Ipsum',
		subTitle: '10',
		path: '/path',
	},
};

import {
	ShadowItem__SubTitle,
	<PERSON>I<PERSON>__Title,
	ShadowItem__Wrapper,
	ShadowItem__WrapperLink,
} from './styled';

type PropsT = {
	title: string;
	subTitle?: number | string;
	path?: string;
};

export const ShadowItem = ({ title, subTitle, path }: PropsT) => {
	if (path) {
		return (
			<ShadowItem__WrapperLink to={path}>
				{title && <ShadowItem__Title>{title}</ShadowItem__Title>}
				{subTitle && <ShadowItem__SubTitle>{subTitle}</ShadowItem__SubTitle>}
			</ShadowItem__WrapperLink>
		);
	}
	return (
		<ShadowItem__Wrapper>
			{title && <ShadowItem__Title>{title}</ShadowItem__Title>}
			{subTitle && <ShadowItem__SubTitle>{subTitle}</ShadowItem__SubTitle>}
		</ShadowItem__Wrapper>
	);
};

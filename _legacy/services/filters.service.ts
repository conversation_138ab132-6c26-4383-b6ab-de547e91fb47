class FilterService {
	private static instance: FilterService | null = null;
	private KEY: string;
	private constructor() {
		this.KEY = 'filters';
		const filters = localStorage.getItem(this.KEY);
		if (!filters) {
			this.initStorage();
		}
	}
	private initStorage() {
		const filters = {
			pastEventsYear: {},
		};
		localStorage.setItem('filters', JSON.stringify(filters));
	}

	setFilter(type: string, value: unknown) {
		const filters = localStorage.getItem(this.KEY);
		if (filters) {
			const data = JSON.parse(filters);
			data[type] = value;
			localStorage.setItem(this.KEY, JSON.stringify(data));
		}
	}
	getFilter(type: string) {
		const filters = localStorage.getItem(this.KEY);
		if (filters) {
			const data = JSON.parse(filters);
			return data[type];
		}
		return null;
	}

	public static getInstance(): FilterService {
		if (!FilterService.instance) {
			FilterService.instance = new FilterService();
		}
		return FilterService.instance;
	}
}

export const filterService = FilterService.getInstance();

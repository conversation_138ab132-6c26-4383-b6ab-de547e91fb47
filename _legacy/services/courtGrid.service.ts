import { isEqual } from 'lodash';

export type FilterOptions = {
	day: string;
	timeRange: [string, string] | null;
	divisionId: string;
	courts: string[];
};

type StorageData = Record<string, FilterOptions>;

const STORAGE_KEY = 'courtGridFilterOptions';

const DEFAULT_FILTER_OPTIONS: FilterOptions = {
	day: '',
	timeRange: null,
	divisionId: '',
	courts: [],
};

class CourtGridService {
	private static instance: CourtGridService | null = null;

	public static getInstance(): CourtGridService {
		if (!CourtGridService.instance) {
			CourtGridService.instance = new CourtGridService();
		}
		return CourtGridService.instance;
	}

	private constructor() {
		// Init storage if not exists
		if (!localStorage.getItem(STORAGE_KEY)) {
			const data = {};
			localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
		}
	}

	private getStorageData(): StorageData {
		const data = localStorage.getItem(STORAGE_KEY);
		return data ? JSON.parse(data) : {};
	}

	private saveStorageData(data: StorageData) {
		localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
	}

	public getFilterOptions(eswId: string): FilterOptions {
		const data = this.getStorageData();
		return data[eswId] || DEFAULT_FILTER_OPTIONS;
	}

	public saveFilterOptions(eswId: string, options: FilterOptions): void {
		const data = this.getStorageData();
		const currentOptions = data[eswId] || DEFAULT_FILTER_OPTIONS;

		if (isEqual(currentOptions, options)) return;

		data[eswId] = options;
		this.saveStorageData(data);
	}
}

export const courtGridService = CourtGridService.getInstance();

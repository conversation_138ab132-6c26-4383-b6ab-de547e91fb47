import { Club, Team } from '@/generated/graphql';

export type LocalFavoriteTeam = Pick<Team, 'team_id' | 'club_id'>;
export type LocalFavoriteClub = Pick<Club, 'roster_club_id'> & { teams: LocalFavoriteTeam[] };

const STORAGE_KEY = 'favoriteTeams';

class FavoriteTeamsService {
	private static instance: FavoriteTeamsService | null = null;

	public static getInstance(): FavoriteTeamsService {
		if (!FavoriteTeamsService.instance) {
			FavoriteTeamsService.instance = new FavoriteTeamsService();
		}
		return FavoriteTeamsService.instance;
	}

	private constructor() {
		// Init storage if not exists
		if (!localStorage.getItem(STORAGE_KEY)) {
			const favorites = {};
			localStorage.setItem(STORAGE_KEY, JSON.stringify(favorites));
		}
	}

	private getFavorites(): Record<string, LocalFavoriteTeam[]> {
		const favorites = localStorage.getItem(STORAGE_KEY);
		return favorites ? JSON.parse(favorites) : {};
	}

	private saveFavorites(favorites: Record<string, LocalFavoriteTeam[]>) {
		localStorage.setItem(STORAGE_KEY, JSON.stringify(favorites));
	}

	public getFavoriteTeams(eswId: string): LocalFavoriteTeam[] {
		const favorites = this.getFavorites();
		return favorites[eswId] || [];
	}

	public getFavoriteTeamsIds(eswId: string): string[] {
		return this.getFavoriteTeams(eswId).map((t: LocalFavoriteTeam) => t.team_id) || [];
	}

	public isTeamFavorite(eswId: string, teamId: string): boolean {
		return this.getFavoriteTeamsIds(eswId).includes(teamId);
	}

	public addTeamToFavorite(eswId: string, team: LocalFavoriteTeam) {
		const favorites = this.getFavorites();
		if (!favorites[eswId]) {
			favorites[eswId] = [];
		}
		if (!this.isTeamFavorite(eswId, team.team_id)) {
			const { team_id, club_id } = team;
			favorites[eswId].push({ team_id, club_id });
		}
		this.saveFavorites(favorites);
	}

	public removeTeamFromFavorite(eswId: string, teamId: string) {
		const favorites = this.getFavorites();
		if (favorites[eswId]) {
			favorites[eswId] = favorites[eswId].filter((team) => team.team_id !== teamId);
			if (!favorites[eswId].length) {
				delete favorites[eswId]; // Remove the eventId if no teams left
			}
			this.saveFavorites(favorites);
		}
	}

	public toggleTeamFavorite(eswId: string, team: LocalFavoriteTeam) {
		if (this.isTeamFavorite(eswId, team.team_id)) {
			this.removeTeamFromFavorite(eswId, team.team_id);
		} else {
			this.addTeamToFavorite(eswId, team);
		}
	}

	public toggleClubTeamsFavorite(eswId: string, club: LocalFavoriteClub) {
		const { roster_club_id, teams } = club;
		const clubTeamsIds = teams.map((team) => team.team_id);
		const favoriteTeams = this.getFavoriteTeams(eswId).filter(
			(team) => team.club_id === roster_club_id,
		);
		const favoriteTeamIds = favoriteTeams.map((team) => team.team_id);

		const allTeamsSelected = clubTeamsIds.every((teamId) => favoriteTeamIds.includes(teamId));

		if (allTeamsSelected) {
			// Unselect all teams if all are already selected
			clubTeamsIds.forEach((teamId) => this.removeTeamFromFavorite(eswId, teamId));
		} else {
			// Add all teams if not all are selected
			teams.forEach((team) => this.addTeamToFavorite(eswId, team));
		}
	}
}

export const favoriteTeamsService = FavoriteTeamsService.getInstance();

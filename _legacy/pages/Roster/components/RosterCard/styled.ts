import styled from 'styled-components';

export const RosterCard__Wrapper = styled.div`
	border-radius: 4px;
	border: 1px solid #dfe3e8;
	padding: 16px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		&:hover {
			.rosterCard__header {
				text-decoration: underline;
			}
		}
	}
`;
export const RosterCard__Header = styled.p`
	margin-bottom: 4px;
	font-size: 16px;
	line-height: 24px;
	font-weight: 700;
`;
export const RosterCard__ContentWrapper = styled.div`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		display: flex;
		justify-content: space-between;
		align-items: center;
		p {
			width: 50%;
			&:last-child {
				text-align: right;
			}
		}
	}
`;
export const RosterCard__Content = styled.p`
	font-size: 14px;
	line-height: 22px;
	font-weight: 700;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-weight: normal;
	}
`;
export const RosterCard__Footer = styled.p`
	font-size: 14px;
	line-height: 22px;
`;

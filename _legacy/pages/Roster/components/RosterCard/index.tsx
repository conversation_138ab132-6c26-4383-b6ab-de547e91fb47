import {
	RosterCard__Content,
	RosterCard__ContentWrapper,
	<PERSON><PERSON><PERSON><PERSON><PERSON>__Footer,
	<PERSON><PERSON><PERSON><PERSON><PERSON>__Header,
	RosterCard__Wrapper,
} from './styled';

type PropsT = {
	header: string;
	content: string;
	footer: string;
};
export const RosterCard = ({ header, content, footer }: PropsT) => {
	return (
		<RosterCard__Wrapper>
			<RosterCard__Header className="rosterCard__header">{header}</RosterCard__Header>
			<RosterCard__ContentWrapper>
				<RosterCard__Content>{content}</RosterCard__Content>
				<RosterCard__Footer>{footer}</RosterCard__Footer>
			</RosterCard__ContentWrapper>
		</RosterCard__Wrapper>
	);
};

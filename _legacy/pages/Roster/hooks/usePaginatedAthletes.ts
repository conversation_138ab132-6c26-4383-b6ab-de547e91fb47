import { DEFAULT_PAGE_SIZE } from '@/config';
import { PaginatedAthlete } from '@/shared/types/athlete.types';
import { useCallback, useEffect, useMemo, useState } from 'react';

import {
	PaginatedAthletesQuery,
	PaginatedAthletesQueryVariables,
	usePaginatedAthletesLazyQuery,
} from '@/generated/graphql';

type Params = Omit<PaginatedAthletesQueryVariables, 'page' | 'pageSize'> & {
	disabled?: boolean;
};

const INITIAL_PAGE = 1;

export const usePaginatedAthletes = (params: Params) => {
	const { eswId, search, disabled = false } = params;
	const [pagesResponses, setPagesResponses] = useState<PaginatedAthletesQuery[]>([]);

	const [fetchAthletes, { data, loading }] = usePaginatedAthletesLazyQuery();
	const [isFetched, setIsFetched] = useState(false);
	useEffect(() => {
		setPagesResponses([]);
		if (disabled) return;
		fetchAthletes({
			variables: {
				eswId,
				page: INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
			onCompleted: () => setIsFetched(true),
		});
	}, [fetchAthletes, search, eswId, disabled]);

	// Update the pagesResponses array when a new page is fetched taking care of the order
	useEffect(() => {
		if (!data) return;
		setPagesResponses((pagesResponses) => {
			const responses = [...pagesResponses];
			responses[data.paginatedAthletes.page_info.page - 1] = data;
			return responses;
		});
	}, [data]);

	// Combine all Athletes from all pages
	const athletes = useMemo(() => {
		return pagesResponses.reduce<PaginatedAthlete[]>((acc, response) => {
			if (!response) return acc;
			return [...acc, ...((response?.paginatedAthletes?.items as PaginatedAthlete[]) ?? [])];
		}, []);
	}, [pagesResponses]);
	const athletesCount = useMemo(() => {
		const lastResponse = pagesResponses.at(-1);

		return lastResponse ? lastResponse.paginatedAthletes.page_info.item_count : null;
	}, [pagesResponses]);

	// Prepare the fetchNext function
	const fetchNext = useCallback(() => {
		if (disabled) return;
		let nextPage;
		const lastResponse = pagesResponses.at(-1);
		if (lastResponse) {
			const { page, page_count } = lastResponse.paginatedAthletes.page_info;
			if (page >= page_count) return;
			nextPage = page + 1;
		}
		fetchAthletes({
			variables: {
				eswId,
				page: nextPage || INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		});
	}, [pagesResponses, fetchAthletes, search, eswId, disabled]);

	return { fetchNext, athletes, athletesCount, loading, isFetched, pagesResponses };
};

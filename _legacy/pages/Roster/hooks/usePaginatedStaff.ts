import { DEFAULT_PAGE_SIZE } from '@/config';
import { PaginatedStaff } from '@/shared/types/staff.types';
import { useCallback, useEffect, useMemo, useState } from 'react';

import {
	PaginatedStaffQuery,
	PaginatedStaffQueryVariables,
	usePaginatedStaffLazyQuery,
} from '@/generated/graphql';

type Params = Omit<PaginatedStaffQueryVariables, 'page' | 'pageSize'> & {
	disabled?: boolean;
};

const INITIAL_PAGE = 1;

export const usePaginatedStaff = (params: Params) => {
	const { eswId, search, disabled = false } = params;
	const [pagesResponses, setPagesResponses] = useState<PaginatedStaffQuery[]>([]);
	const [isFetched, setIsFetched] = useState(false);
	const [fetchStaff, { data, loading }] = usePaginatedStaffLazyQuery();
	useEffect(() => {
		setPagesResponses([]);
		if (disabled) return;
		fetchStaff({
			variables: {
				eswId,
				page: INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		}).then(() => setIsFetched(true));
	}, [fetchStaff, search, eswId, disabled]);

	// Update the pagesResponses array when a new page is fetched taking care of the order
	useEffect(() => {
		if (!data) return;
		setPagesResponses((pagesResponses) => {
			const responses = [...pagesResponses];
			responses[data.paginatedStaff.page_info.page - 1] = data;
			return responses;
		});
	}, [data]);

	// Combine all Staff from all pages
	const staff = useMemo(() => {
		return pagesResponses.reduce<PaginatedStaff[]>((acc, response) => {
			if (!response) return acc;
			return [...acc, ...((response?.paginatedStaff?.items as PaginatedStaff[]) ?? [])];
		}, []);
	}, [pagesResponses]);
	const staffCount = useMemo(() => {
		const lastResponse = pagesResponses.at(-1);
		return lastResponse ? lastResponse.paginatedStaff.page_info.item_count : null;
	}, [pagesResponses]);

	// Prepare the fetchNext function
	const fetchNext = useCallback(() => {
		if (disabled) return;
		let nextPage;
		const lastResponse = pagesResponses.at(-1);
		if (lastResponse) {
			const { page, page_count } = lastResponse.paginatedStaff.page_info;
			if (page >= page_count) return;
			nextPage = page + 1;
		}
		fetchStaff({
			variables: {
				eswId,
				page: nextPage || INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		});
	}, [pagesResponses, fetchStaff, search, eswId, disabled]);

	return { fetchNext, staff, staffCount, loading, isFetched };
};

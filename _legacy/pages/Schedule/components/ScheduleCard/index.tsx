import doneIcon from '@assets/round-done-icon.svg';

import { CourtMatchesCourtMatchResults } from '@/generated/graphql';

import { getFormateTime } from '@/utils/time';

import {
	ScheduleCard__Footer,
	ScheduleCard__Header,
	ScheduleCard__Item,
	ScheduleCard__ItemTimeRef,
	ScheduleCard__ItemTimeWrapper,
	ScheduleCard__List,
	ScheduleCard__Wrapper,
} from './styled';

type PropsT = {
	header: string;
	team1Name: string;
	team2Name: string;
	results: CourtMatchesCourtMatchResults;
	dateEnd?: number;
	id: string;
	team_ref_name?: string;
	currentSelectedMatchId: string | null;
};
export const ScheduleCard = ({
	header,
	team1Name,
	team2Name,
	results,
	dateEnd,
	id,
	team_ref_name,
	currentSelectedMatchId,
}: PropsT) => {
	const scores = results?.team1?.scores && results.team1?.scores.split(' ')[0].split('-');
	return (
		<ScheduleCard__Wrapper
			id={id}
			$isActive={currentSelectedMatchId === id}
			$isBlur={!!currentSelectedMatchId && currentSelectedMatchId !== id}
		>
			<ScheduleCard__Header>{header}</ScheduleCard__Header>
			<ScheduleCard__List>
				<ScheduleCard__Item>
					<p>{team1Name}</p>
					<p>{scores?.[0]}</p>
				</ScheduleCard__Item>
				<ScheduleCard__Item>
					<p>{team2Name}</p>
					<p>{scores?.[1]}</p>
				</ScheduleCard__Item>
			</ScheduleCard__List>
			<ScheduleCard__Footer>
				{dateEnd && results && (
					<ScheduleCard__ItemTimeWrapper>
						<img src={doneIcon} alt="done" /> {getFormateTime({ time: dateEnd, format: 'h:mmaaa' })}
					</ScheduleCard__ItemTimeWrapper>
				)}
				{team_ref_name && !results && (
					<ScheduleCard__ItemTimeRef>{team_ref_name}</ScheduleCard__ItemTimeRef>
				)}
			</ScheduleCard__Footer>
		</ScheduleCard__Wrapper>
	);
};

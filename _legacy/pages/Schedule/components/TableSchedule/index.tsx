import arrowPrev from '@assets/arrowLeftBlack.svg';
import arrowNext from '@assets/arrowRightBlack.svg';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';

import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableRow from '@mui/material/TableRow';

import { CustomMultiSelect } from '@/components/CustomMultiselect';
import { NotFound } from '@/components/NotFound';

import { CourtMatchesCourt } from '@/generated/graphql';

import { useScheduleTable } from '../../hooks/useScheduleTable';
import { DirectionT } from '../../types';
import { ScheduleCard } from '../ScheduleCard';
import { SCROLL_OFFSET_TIME_RANGE } from '../TimeRangeSchedule/styled';
import {
	TableSchedule__Cell,
	TableSchedule__CellCourt,
	TableSchedule__NextButton,
	TableSchedule__PrevButton,
	TableSchedule__RangeCell,
	TableSchedule__RangeCellContainer,
	TableSchedule__RangeCellWrapper,
	TableSchedule__TableWrapper,
	TableSchedule__Wrapper,
} from './styled';

type PropsT = {
	courts: CourtMatchesCourt[];
	activeCourts: CourtMatchesCourt[];
	directionHandler: (_: DirectionT) => void;
	setSelectedCourts: Dispatch<SetStateAction<string[]>>;
	isShowNextButton: boolean;
	isShowPrevButton: boolean;
	currentSelectedMatchId: string | null;
	resetFilters: () => void;
};

export const TableSchedule = ({
	courts,
	activeCourts,
	directionHandler,
	setSelectedCourts,
	isShowNextButton,
	isShowPrevButton,
	currentSelectedMatchId,
	resetFilters,
}: PropsT) => {
	const { getPeriods, isPeriodEqual } = useScheduleTable();
	const periods = getPeriods(courts);

	const isShowNextTime = ({ allLength, index }: { allLength: number; index: number }) => {
		return allLength - 1 === index;
	};
	const isShowPrevTime = ({ index }: { index: number }) => {
		return index === 0;
	};

	const [isFixed, setIsFixed] = useState(window.scrollY > SCROLL_OFFSET_TIME_RANGE);

	useEffect(() => {
		const handleScroll = () => {
			const newScrollTop = window.scrollY || document.documentElement.scrollTop;
			setIsFixed(newScrollTop > SCROLL_OFFSET_TIME_RANGE);
		};

		window.addEventListener('scroll', handleScroll);

		return () => {
			window.removeEventListener('scroll', handleScroll);
		};
	}, []);

	return (
		<div>
			{!periods.length ? (
				<NotFound type="court-grid" onClick={resetFilters} />
			) : (
				<TableSchedule__TableWrapper $isFixed={isFixed}>
					<TableContainer>
						<Table>
							<TableBody>
								<TableRow>
									<TableSchedule__Cell>
										<CustomMultiSelect
											data={activeCourts.map((c) => ({
												key: c.court_name as string,
												value: c.court_id as string,
											}))}
											selectName="Ct"
											dropDownName="Select courts"
											setSelectedData={setSelectedCourts}
											type="courts"
										/>
									</TableSchedule__Cell>
									{periods.map((period, index) => (
										<TableSchedule__RangeCell key={period}>
											<TableSchedule__RangeCellWrapper>
												<TableSchedule__RangeCellContainer>
													{isShowPrevButton && isShowPrevTime({ index }) && (
														<TableSchedule__PrevButton
															src={arrowPrev}
															alt="prev"
															onClick={() => directionHandler({ type: 'prev' })}
														/>
													)}
													{period}
													{isShowNextButton &&
														isShowNextTime({ allLength: periods.length, index }) && (
															<TableSchedule__NextButton
																src={arrowNext}
																alt="next"
																onClick={() => directionHandler({ type: 'next' })}
															/>
														)}
												</TableSchedule__RangeCellContainer>
											</TableSchedule__RangeCellWrapper>
										</TableSchedule__RangeCell>
									))}
								</TableRow>
								{courts
									.filter((c) => c.matches)
									.map((court) => {
										return (
											<TableRow key={court.court_id}>
												<TableSchedule__CellCourt>
													<TableSchedule__Wrapper>Ct {court.short_name}</TableSchedule__Wrapper>
												</TableSchedule__CellCourt>
												{periods.map((period, index) => {
													return (
														<TableSchedule__Cell key={period}>
															{court.matches &&
																court.matches.map(
																	({
																		match_id,
																		match_name,
																		division_short_name,
																		team_1_name,
																		team_2_name,
																		results,
																		date_start,
																		date_end,
																		team_ref_name,
																	}) => {
																		if (date_start && isPeriodEqual(date_start, periods[index])) {
																			return (
																				<ScheduleCard
																					currentSelectedMatchId={currentSelectedMatchId}
																					team_ref_name={team_ref_name!}
																					id={match_id!}
																					dateEnd={date_end!}
																					key={match_id}
																					header={`${division_short_name} - ${match_name}`}
																					team1Name={team_1_name!}
																					team2Name={team_2_name!}
																					results={results!}
																				/>
																			);
																		}
																		return null;
																	},
																)}
														</TableSchedule__Cell>
													);
												})}
											</TableRow>
										);
									})}
							</TableBody>
						</Table>
					</TableContainer>
				</TableSchedule__TableWrapper>
			)}
		</div>
	);
};

import { UTC_TIME_ZONE } from '@/config';
import * as DateFns from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';

import { CourtMatchesCourt } from '@/generated/graphql';

export const useScheduleTable = () => {
	const getPeriods = (courts: CourtMatchesCourt[]) => {
		const periods = new Set<number>();

		courts.forEach((c) => {
			c.matches?.forEach((m) => {
				m.date_start && periods.add(m.date_start);
			});
		});

		return [...periods].sort().map((t) => {
			return DateFns.format(utcToZonedTime(t, UTC_TIME_ZONE), 'hh:mm a');
		});
	};

	const isPeriodEqual = (t1: number, t2: string): boolean => {
		return DateFns.format(utcToZonedTime(t1, UTC_TIME_ZONE), 'hh:mm a') === t2;
	};

	return {
		getPeriods,
		isPeriodEqual,
	};
};

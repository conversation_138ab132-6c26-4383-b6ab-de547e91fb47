import styled from 'styled-components';

import { Tabs } from '@mui/material';

export const DivisionTabs__Wrapper = styled.div``;
export const DivisionTabs__ItemsWrapper = styled.div<{ $isDesktopFixed?: boolean }>`
	position: fixed;
	bottom: 0;
	width: 100%;
	border-top: 1px solid #f4f6f8;
	background: #fff;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 0 16px;
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		border: none;
		position: ${(props) => (props.$isDesktopFixed ? 'fixed' : 'static')};
		top: 202px;
		background: #fff;
		height: 70px;
		z-index: 7;
		width: 600px;
		position: fixed;
		top: 192px;
		padding: 10px 0 0;
		width: 600px;
		.MuiTabs-scroller {
			margin-bottom: 25px !important;
		}
	}
`;
export const DivisionTabs__ItemsWrapperTabs = styled(Tabs)`
	.MuiTabs-flexContainer {
		gap: 70px;
		justify-content: center;
		button {
			width: fit-content;
			min-width: fit-content;
			padding: 0;
			font-size: 16px;
			line-height: 22px;
			font-family: 'Public Sans';
			font-weight: normal;
			color: #637381;
			text-transform: capitalize;
		}
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			justify-content: flex-start;
			gap: 20px;
			button {
				min-width: auto;
			}
		}
		@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
			button {
				font-size: 14px;
				min-width: auto;
			}
		}
	}
	.MuiTabs-indicator {
	}
	.Mui-selected {
		opacity: 1 !important;
		font-weight: 700 !important;
		color: #454f5b !important;
	}
	.MuiTab-root {
		opacity: 1 !important;
	}
`;
export const DivisionTabs__ContentWrapper = styled.div`
	padding-bottom: 30px;
	.MuiBox-root {
		padding: 0;
	}
	.MuiTabPanel-root {
		padding: 0;
	}
`;
export const defaultTabPanelStyles = {
	px: 0,
};
export const grayBackgroundTabPanelStyles = {
	...defaultTabPanelStyles,
	background: '#F9FAFB',
	// height: '100vh',
};

import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useTeamModal } from '@/shared/hooks/useTeamModal';
import { DivisionTeamStanding } from '@/shared/types/team.types';
import { addOrdinalSuffix } from '@/utils';
import { useMemo } from 'react';

import { StyledNotFound } from '@/styles/shared';

import { Loader } from '@/components/Loader';
import { NotFound } from '@/components/NotFound';
import { PreviouslyQualifiedInfo } from '@/components/PreviouslyQualifiedInfo';
import { StandingMatchResult } from '@/components/StandingMatchResult';
import { StandingPointsRatio } from '@/components/StandingPointsRatio';
import { StandingSetsPercentage } from '@/components/StandingSetsPercentage';
import { StandingSetsResult } from '@/components/StandingSetsResult';

import { placeholders } from '@/config/searchPlaceholder';

import { POOL_TABS } from '../../constants';
import { DivisionSearchBar } from '../DivisionSearchBar';
import { DivisionTabs__AnchorButton, DivisionTabs__StickyHeader } from '../PoolBracketsTab/styled';
import { useSections } from './hooks/useSections';
import { useStandings } from './hooks/useStandings';
import {
	StandingsTab__ContainerList,
	StandingsTab__PreviouslyQualifiedWrapper,
	StandingsTab__ScrollWrapper,
	StandingsTab__StandingList,
	StandingsTab__StandingListItem,
	StandingsTab__StandingListItemBox,
	StandingsTab__StandingTitle,
	StandingsTab__Wrapper,
	StandingsTabs__ContentWrapper,
	StandingsTabs__TabsWrapper,
} from './styled';
import { StandingsHeader } from './ui/StandingsHeader';

type Props = {
	divisionId: string;
	children: React.ReactNode;
};

export const StandingsTab = ({ divisionId, children }: Props) => {
	const { event, loading: eventLoading } = useEventDetails();
	const hideSeeds = !!event?.hide_seeds;

	const { teamModalElement, openTeamModal, teamDataLoading } = useTeamModal();
	const {
		headings,
		standings,
		isContainsPoints,
		standingsLoading,
		hasRanks,
		search,
		onChangeSearch,
	} = useStandings(divisionId);
	const loading = standingsLoading || teamDataLoading || eventLoading;

	const { activeSection, scrollToSection, isFixed, mobileWrapper, desktopWrapper, sectionRefs } =
		useSections();

	useAnalytics('Division (Standings)', search);

	const { breakPont, browserHeight } = useCurrentSize();
	const isLarge = breakPont !== 'small';
	const { isDesktopFixed } = useDesktopSticky({ page: 'division' });
	const { isDesktopFixed: isDesktopFixedTab } = useDesktopSticky({ page: 'favoritesTabs' });

	const headingMaxLen = useMemo(() => {
		return Math.max(...headings.map(({ length }) => length));
	}, [headings]);

	const renderHeadingList = () => {
		return standings.map(({ heading }, index) => {
			const key = `#anchor${index + 1}`;
			return (
				<DivisionTabs__AnchorButton
					key={key}
					$isActive={activeSection === index}
					$width={headingMaxLen * 10}
					href={key}
					onClick={(e) => {
						e.preventDefault();
						scrollToSection(index);
					}}
				>
					{heading}
				</DivisionTabs__AnchorButton>
			);
		});
	};

	const renderRankOrSeed = ({ division_standing }: DivisionTeamStanding) => {
		if (hasRanks || !hideSeeds) {
			const value = (hasRanks ? division_standing?.rank : division_standing?.seed) || 0;
			return (
				<StandingsTab__StandingListItemBox>
					<b>{addOrdinalSuffix(value)}</b>
				</StandingsTab__StandingListItemBox>
			);
		}
		return null;
	};

	return (
		<>
			{loading && <Loader />}
			{teamModalElement}
			<DivisionSearchBar
				placeholder={placeholders.division_standings}
				divisionId={divisionId}
				search={search}
				onChangeSearch={onChangeSearch}
				isFixed={isFixed}
				activeTab={POOL_TABS.STANDINGS}
			/>
			<StandingsTab__Wrapper
				$browserHeight={browserHeight}
				$isDesktopFixed={isDesktopFixed}
				ref={desktopWrapper}
			>
				<StandingsTabs__TabsWrapper>{children}</StandingsTabs__TabsWrapper>
				{!isLarge && (
					<DivisionTabs__StickyHeader $isFixed={isFixed} $isDesktopFixed={isDesktopFixedTab}>
						{renderHeadingList()}
					</DivisionTabs__StickyHeader>
				)}

				<StandingsTab__ScrollWrapper ref={mobileWrapper} $isFixed={isFixed}>
					{isLarge && (
						<DivisionTabs__StickyHeader $isFixed={isFixed} $isDesktopFixed={isDesktopFixedTab}>
							{renderHeadingList()}
						</DivisionTabs__StickyHeader>
					)}
					{standings.length > 0 && <StandingsHeader isContainsPoints={isContainsPoints} />}
					<StandingsTabs__ContentWrapper $isDesktopFixed={isDesktopFixedTab}>
						{standings.map(({ heading, teams }, index) => {
							return (
								<div key={`${heading}_${index}`}>
									<StandingsTab__StandingTitle>{heading}</StandingsTab__StandingTitle>
									<StandingsTab__StandingList
										id={`anchor${index + 1}`}
										ref={(el) => (sectionRefs.current[index] = el as HTMLUListElement | null)}
									>
										<StandingsTab__ContainerList className="container">
											<ul>
												{teams.map((team) => {
													return (
														<StandingsTab__StandingListItem key={team.team_code}>
															{renderRankOrSeed(team)}
															{team.division_standing?.points && (
																<StandingsTab__StandingListItemBox>
																	{team.division_standing.points}
																</StandingsTab__StandingListItemBox>
															)}
															<StandingsTab__StandingListItemBox
																$extraOffset={
																	!!(
																		team?.extra?.show_accepted_bid &&
																		team?.extra?.show_previously_accepted_bid
																	)
																}
																onClick={() => openTeamModal(team.team_id)}
															>
																<span>{team.team_name}</span>
																{team?.extra?.show_accepted_bid && (
																	<StandingsTab__PreviouslyQualifiedWrapper $position={1}>
																		<PreviouslyQualifiedInfo type="accepted_bid" />
																	</StandingsTab__PreviouslyQualifiedWrapper>
																)}
																{team?.extra?.show_previously_accepted_bid && (
																	<StandingsTab__PreviouslyQualifiedWrapper
																		$position={team?.extra?.show_accepted_bid ? 2 : 1}
																	>
																		<PreviouslyQualifiedInfo
																			type="previously_accepted_bid"
																			info={team.extra.show_previously_accepted_bid}
																		/>
																	</StandingsTab__PreviouslyQualifiedWrapper>
																)}
															</StandingsTab__StandingListItemBox>
														</StandingsTab__StandingListItem>
													);
												})}
											</ul>
											<ul>
												{teams.map((team) => {
													const { division_standing } = team;
													return (
														<StandingsTab__StandingListItem key={team.team_code}>
															<StandingsTab__StandingListItemBox>
																<StandingMatchResult standing={division_standing} />
															</StandingsTab__StandingListItemBox>
															<StandingsTab__StandingListItemBox>
																<StandingSetsResult standing={division_standing} />
															</StandingsTab__StandingListItemBox>
															<StandingsTab__StandingListItemBox>
																<StandingSetsPercentage standing={team.division_standing} />
															</StandingsTab__StandingListItemBox>
															<StandingsTab__StandingListItemBox>
																<StandingPointsRatio standing={division_standing} />
															</StandingsTab__StandingListItemBox>
															<StandingsTab__StandingListItemBox>
																{team.team_code}
															</StandingsTab__StandingListItemBox>
														</StandingsTab__StandingListItem>
													);
												})}
											</ul>
										</StandingsTab__ContainerList>
									</StandingsTab__StandingList>
								</div>
							);
						})}
					</StandingsTabs__ContentWrapper>
					{!standings.length && !loading && !search && (
						<StyledNotFound>No Standings available yet</StyledNotFound>
					)}
					{!standings.length && !standingsLoading && search && <NotFound type="pool-brackets" />}
				</StandingsTab__ScrollWrapper>
			</StandingsTab__Wrapper>
		</>
	);
};

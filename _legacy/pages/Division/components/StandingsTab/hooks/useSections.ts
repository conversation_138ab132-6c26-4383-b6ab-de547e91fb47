import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useEffect, useRef, useState } from 'react';

export const useSections = () => {
	const sectionRefs = useRef<Array<HTMLUListElement | null>>([]);
	const mobileWrapper = useRef<HTMLDivElement | null>(null);
	const desktopWrapper = useRef<HTMLDivElement | null>(null);
	const [activeSection, setActiveSection] = useState<number | null>(0);
	const { breakPont } = useCurrentSize();
	const isLarge = breakPont !== 'small';
	const currentWrapper = isLarge ? desktopWrapper.current || window : mobileWrapper.current;

	useEffect(() => {
		const handleScroll = () => {
			const scrollPosition =
				(currentWrapper instanceof HTMLElement
					? currentWrapper?.scrollTop
					: currentWrapper?.scrollY) || 0;
			sectionRefs.current.forEach((section, index) => {
				const top = isLarge
					? (section as HTMLElement)?.offsetTop - 450
					: (section as HTMLElement)?.offsetTop;
				const bottom = top + (section as HTMLElement)?.clientHeight;

				if (scrollPosition >= top && scrollPosition < bottom) {
					setActiveSection(index);
				}
			});
		};

		currentWrapper?.addEventListener('scroll', handleScroll);

		return () => {
			currentWrapper?.removeEventListener('scroll', handleScroll);
		};
	}, [currentWrapper, isLarge]);

	const [isFixed, setIsFixed] = useState(false);
	const OFFSET_ANCHOR = isFixed ? 10 : 50;
	useEffect(() => {
		const handleScroll = () => {
			const newScrollTop =
				(currentWrapper instanceof HTMLElement
					? currentWrapper?.scrollTop
					: currentWrapper?.scrollY) || 0;
			setIsFixed(newScrollTop > OFFSET_ANCHOR);
		};

		currentWrapper?.addEventListener('scroll', handleScroll);

		return () => {
			currentWrapper?.removeEventListener('scroll', handleScroll);
		};
	}, [OFFSET_ANCHOR, currentWrapper]);

	const scrollToSection = (index: number) => {
		const targetRef = sectionRefs.current[index];
		const targetSection = targetRef;
		if (targetSection && currentWrapper) {
			const top = isLarge
				? index
					? targetSection.offsetTop - 500
					: 0
				: index
					? targetSection.offsetTop + OFFSET_ANCHOR
					: 0;
			currentWrapper.scrollTo({
				top,
				behavior: 'instant',
			});
		}
		setTimeout(() => {
			setActiveSection(index);
		});
	};

	return {
		activeSection,
		scrollToSection,
		isFixed,
		desktopWrapper,
		mobileWrapper,
		sectionRefs,
	};
};

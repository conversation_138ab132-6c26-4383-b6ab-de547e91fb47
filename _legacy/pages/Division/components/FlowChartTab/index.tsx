import { useEventDetails } from '@/shared/hooks/useEventDetails';

import { StyledNotFound } from '@/styles/shared';

import { POOL_TABS } from '../../constants';
import { DivisionSearchBar } from '../DivisionSearchBar';
import {
	StyledFlowChartImg,
	StyledFlowChartTabWrapper,
	StyledFlowChartTabsWrapper,
} from './styled';

type Props = {
	divisionId: string;
	children: React.ReactNode;
};

export const FlowChartTab = ({ divisionId, children }: Props) => {
	const HOST =
		import.meta.env.VITE_APP_ENV === 'development'
			? import.meta.env.VITE_DEV_SERVER
			: import.meta.env.VITE_PROD_SERVER;

	const { divisions } = useEventDetails();

	const currentDivision = divisions?.find((division) => division.division_id === divisionId);
	const currentMedia = currentDivision?.media[0];
	const hasFlowChart = currentDivision?.has_flow_chart && currentMedia?.path;
	return (
		<>
			<DivisionSearchBar divisionId={divisionId} activeTab={POOL_TABS.FLOW_CHART} />
			<StyledFlowChartTabWrapper>
				<StyledFlowChartTabsWrapper>{children}</StyledFlowChartTabsWrapper>
				{hasFlowChart && <StyledFlowChartImg src={`${HOST}${currentMedia?.path}`} alt="" />}
				{!hasFlowChart && <StyledNotFound>No Flow Charts available yet.</StyledNotFound>}
			</StyledFlowChartTabWrapper>
		</>
	);
};

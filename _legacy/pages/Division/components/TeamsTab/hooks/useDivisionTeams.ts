import { SEARCH_DEBOUNCE_TIME } from '@/config';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { sanitizeSearchInput } from '@/utils';
import { useCallback, useEffect, useState } from 'react';
import { useDebounce } from 'use-debounce';
import { StringParam, useQueryParams } from 'use-query-params';

import { usePaginatedDivisionTeams } from './usePaginatedDivisionTeams';

export const useDivisionTeams = (divisionId: string) => {
	const { eswId } = useEventDetails();
	const [queryParams] = useQueryParams({ search: StringParam });

	const [search, setSearch] = useState(queryParams.search || '');
	const [searchDebounce] = useDebounce(sanitizeSearchInput(search), SEARCH_DEBOUNCE_TIME);
	const onChangeSearch = useCallback((value: string) => {
		setSearch(value);
	}, []);

	const [fetchNext, teams, loading] = usePaginatedDivisionTeams({
		eswId,
		divisionId,
		search: searchDebounce,
	});

	useEffect(() => {
		setSearch(queryParams.search || '');
	}, [queryParams.search]);

	return {
		teams,
		loading,
		search,
		onChangeSearch,
		fetchNext,
	};
};

import { useCallback, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

import {
	DivisionPool,
	PoolsQuery,
	usePoolsQuery,
	useTeamSingleLazyQuery,
	useUpcomingMatchesLazyQuery,
} from '@/generated/graphql';

export const usePoolBracketsModals = () => {
	const [openIsPoolModal, setOpenIsPoolModal] = useState(false);
	const { id, divisionId } = useParams();
	const [rounds, setRounds] = useState<PoolsQuery['pools'][]>([]);
	const [
		getTeamPoolData,
		{ data: teamPoolData, loading: teamPoolDataLoading, refetch: teamRefetch },
	] = useTeamSingleLazyQuery();

	const [isShowPoolBracketAnchorModal, setIsShowPoolBracketAnchorModal] = useState(false);
	const [poolOfTeam, setPoolOfTeam] = useState<DivisionPool | null>(null);
	const openPoolBracketAnchorModal = useCallback(
		(teamId: number, poolOfTeam?: DivisionPool) => {
			poolOfTeam && setPoolOfTeam(poolOfTeam);
			getTeamPoolData({
				variables: {
					id: id!,
					teamId: `${teamId}`,
				},
				onCompleted: () => {
					setIsShowPoolBracketAnchorModal(true);
				},
			});
		},
		[getTeamPoolData, id],
	);

	const closePoolBracketAnchorModal = () => {
		setPoolOfTeam(null);
		setIsShowPoolBracketAnchorModal(false);
	};
	const { data, loading: poolsLoading } = usePoolsQuery({
		variables: {
			id: id!,
			divisionId: divisionId!,
		},
		onCompleted() {
			if (data) {
				const groupedRounds = data.pools.reduce(
					(acc, el) => {
						const { r_name, ...rest } = el;
						if (!acc[r_name!] && el.r_name) {
							acc[r_name!] = [];
						}
						if (el.uuid) {
							acc[r_name!].push({ r_name, ...rest });
						}
						return acc;
					},
					{} as Record<string, PoolsQuery['pools']>,
				);
				const result = Object.values(groupedRounds).filter((arr) => arr.length);

				setRounds(result);
			}
		},
	});

	const pools = useMemo(() => rounds.flat(), [rounds]) as DivisionPool[];

	const [currentPoolId, setCurrentPoolId] = useState('');
	const closeBracketModal = useCallback(() => {
		setCurrentPoolId('');
	}, []);
	const openBracketModal = useCallback((poolId: string) => {
		setCurrentPoolId(poolId);
	}, []);
	const openPoolModalModal = useCallback(
		(uuid: string) => {
			const pool = pools.find((pool) => pool.uuid === uuid);
			if (pool?.uuid && pool.teams && pool.r_uuid) {
				openPoolBracketAnchorModal(pool.teams[0].opponent_team_id!, pool);
			}
		},
		[openPoolBracketAnchorModal, pools],
	);

	const currentPool = useMemo(() => {
		return pools.find((pool) => pool.uuid === currentPoolId);
	}, [pools, currentPoolId]);

	const [, { refetch: matchesRefetch }] = useUpcomingMatchesLazyQuery({
		variables: {
			id: id!,
		},
	});

	return {
		teamPoolData,
		modalsLoading: teamPoolDataLoading || poolsLoading,
		isShowPoolBracketAnchorModal,
		openPoolBracketAnchorModal,
		closePoolBracketAnchorModal,
		pools,
		poolOfTeam,
		currentPool,
		closeBracketModal,
		openBracketModal,
		openPoolModalModal,
		openIsPoolModal,
		setOpenIsPoolModal,
		teamRefetch,
		matchesRefetch,
	};
};

import styled from 'styled-components';

import { ThemeT } from '@/styles/theme';

export const Division__SearchBar = styled.div`
	padding: 16px;
	justify-content: space-between;
	align-items: center;
	background: ${(props) => props.theme.colors.blue};
	width: 100%;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 5px 60px 38px 60px;
		position: fixed;
		height: 200px;
	}
`;
export const Division__SearchBarHeader = styled.div`
	justify-content: space-between;
	align-items: center;
	display: flex;
	margin: 0 0 16px;
`;
export const Division__SearchBarTitle = styled.p<{ $isFullWidth?: boolean }>`
	font-size: 14px;
	line-height: 22px;
	font-weight: 700;
	color: #fff;
	width: 100%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	a {
		text-decoration: none;
		color: #fff;
		&:hover {
			text-decoration: underline;
		}
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 20px;
		font-weight: 700;
		line-height: 30px;
		margin: 0 0 4px;
	}
`;

export const Division__SearchBarFooterWrapper = styled.div`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: 600px;
		margin: auto;
	}
`;
export const Division__SearchBarFooter = styled.div<{ $isFixed: boolean }>`
	display: flex;
	align-items: end;
	justify-content: space-between;
	gap: 10px;
	background: ${(props) => props.theme.colors.blue};
	padding: ${({ $isFixed }) => ($isFixed ? '20px 16px 16px' : 0)};
	width: 100%;
	left: 0;
	z-index: 1;
	position: ${({ $isFixed }) => ($isFixed ? 'fixed' : 'relative')};
	top: ${({ $isFixed }) => ($isFixed ? '39px' : '0')};
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		gap: 16px;
		position: relative;
		padding: 0;
		top: 0;
	}
	.custom_select {
		width: 115px !important;
		height: 40px;
		display: flex;
		align-items: center;
		/* padding: 0 18px 0 8px; */
		select {
			font-size: 14px;
			line-height: 22px;
		}
	}
`;

export const Division__SearchWrapper = styled.div`
	position: relative;
	width: 100%;
`;
export const Division__Search = styled.input`
	height: 40px;
	border-radius: 4px;
	border: 1px solid #f9fafb;
	background: transparent;
	padding: 0 40px 0 16px;
	font-size: 14px;
	line-height: 22px;
	width: 100%;
	color: #f9fafb;
	caret-color: #f9fafb;
	&:focus {
		outline: none;
	}
	&::placeholder {
		color: #f9fafb;
		font-size: 14px;
	}
`;
export const Division__SearchIcon = styled.img<{ $isClear?: boolean }>`
	position: absolute;
	right: 15px;
	top: 50%;
	transform: translateY(-50%);
	cursor: ${({ $isClear }) => ($isClear ? 'pointer' : 'auto')};
`;

export const Division__SearchCourtGridButton = styled.button<ThemeT>`
	border-radius: 4px;
	background-color: #fff;
	border: 1px solid #f9fafb;
	font-size: 14px;
	line-height: 22px;
	color: ${(props) => props.theme.colors.blue};
	max-width: 100px;
	height: 40px;
	padding: 0 6px;
	cursor: pointer;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		min-width: 55px;
	}
`;

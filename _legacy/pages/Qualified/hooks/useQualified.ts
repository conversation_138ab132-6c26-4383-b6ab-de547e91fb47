import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { sanitizeSearchInput } from '@/utils';
import _ from 'lodash';
import { useCallback, useMemo, useState } from 'react';

import { useDivisionsQualifiedTeamsQuery } from '@/generated/graphql';

type QualifiedTeam = {
	division_name: string;
	team_name: string;
	team_id: string;
	earned_at: string;
	bid_earned: string;
};

export const useQualified = () => {
	const { eswId, event, loading: eventLoading } = useEventDetails();

	const [search, setSearch] = useState('');
	const onChangeSearch = useCallback((value: string) => {
		setSearch(value);
	}, []);

	const { is_with_prev_qual } = event || {};
	const { data, loading: teamsLoading } = useDivisionsQualifiedTeamsQuery({
		skip: !is_with_prev_qual,
		variables: {
			eswId,
		},
	});

	const qualifiedTeams = useMemo(() => {
		if (!data || !is_with_prev_qual) return [];
		return data.divisions
			.toSorted((a, b) => a.name.localeCompare(b.name))
			.reduce<QualifiedTeam[]>((acc, division) => {
				const division_name = division.name;
				division.qualified_teams.forEach((team) => {
					const { team_id, team_name, extra } = team;
					const { prev_qual_age, prev_qual_division, earned_at } = extra || {};
					acc.push({
						division_name,
						team_name,
						team_id,
						earned_at: earned_at || '',
						bid_earned: `${prev_qual_age || ''} ${_.capitalize(prev_qual_division || '')}`,
					});
				});
				return acc;
			}, []);
	}, [data, is_with_prev_qual]);

	const sanitizedSearch = sanitizeSearchInput(search).toLowerCase();
	const filterQualifiedTeams = useMemo(() => {
		return qualifiedTeams.filter((item) => {
			return (
				item.team_name?.toLowerCase().includes(sanitizedSearch) ||
				item.division_name?.toLowerCase().includes(sanitizedSearch) ||
				item.bid_earned?.toLowerCase().includes(sanitizedSearch) ||
				item.earned_at?.toLowerCase().includes(sanitizedSearch)
			);
		});
	}, [sanitizedSearch, qualifiedTeams]);

	return {
		search,
		onChangeSearch,
		loading: eventLoading || teamsLoading,
		qualifiedTeams: filterQualifiedTeams,
	};
};

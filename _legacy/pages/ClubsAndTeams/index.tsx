import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useBracketModal } from '@/shared/hooks/useBracketModal';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { useEventDescription } from '@/shared/hooks/useEventDescription';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useFavoriteTeams } from '@/shared/hooks/useFavoriteTeams';
import { useLazyScrollTrigger } from '@/shared/hooks/useLazyScrollTrigger';
import { usePoolModal } from '@/shared/hooks/usePoolModal';
import { useTeamModal } from '@/shared/hooks/useTeamModal';
import { useRef } from 'react';

import { NotFound } from '@/components/NotFound';
import { SearchBar } from '@/components/SearchBar';
import { Spinner } from '@/components/Spinner';

import { placeholders } from '@/config/searchPlaceholder';

import { Teams } from './components/Teams';
import { useClubsAndTeams } from './hooks/useClubsAndTeams';
import { useClubsIcons } from './hooks/useClubsIcons';
import {
	ClubsAndTeams__Header,
	ClubsAndTeams__Item,
	ClubsAndTeams__ItemInner,
	ClubsAndTeams__List,
	ClubsAndTeams__ListWrapper,
	ClubsAndTeams__Wrapper,
} from './styled';

type PropsT = {
	isTab?: boolean;
};

const ClubsAndTeams = ({ isTab }: PropsT) => {
	const { event, loading: eventLoading } = useEventDetails();
	const description = useEventDescription();

	const { clubs, search, loading: clubsLoading, onChangeSearch, fetchNext } = useClubsAndTeams();
	const { favoriteTeamsIds, toggleTeamFavorite, toggleClubTeamsFavorite } = useFavoriteTeams();
	const getClubIcon = useClubsIcons(clubs, favoriteTeamsIds);
	useAnalytics('Clubs & Teams', search);

	const { browserHeight, breakPont } = useCurrentSize();
	const { isDesktopFixed } = useDesktopSticky({ page: 'clubsAndTeams' });

	const scrollRef = useRef<HTMLDivElement>(null);
	const isMobile = breakPont === 'small';
	useLazyScrollTrigger(fetchNext, isMobile ? null : scrollRef);

	const loading = eventLoading || clubsLoading;

	const { teamModalElement, openTeamModal } = useTeamModal();
	const { poolModalElement, openPoolModal } = usePoolModal();
	const { bracketModalElement, openBracketModal } = useBracketModal(openPoolModal);

	return (
		<>
			{teamModalElement}
			{poolModalElement}
			{bracketModalElement}
			<ClubsAndTeams__Wrapper>
				{!isTab && (
					<SearchBar
						placeholder={placeholders.clubsTeams}
						description={description}
						eventName={event?.long_name || ''}
						search={search}
						onChangeSearch={onChangeSearch}
						isShowBuyAdmission={!!event?.tickets_published}
						tickets_code={event?.tickets_code || ''}
						isAssignedTickets={!!event?.is_require_recipient_name_for_each_ticket}
					/>
				)}
				<>
					<ClubsAndTeams__ListWrapper
						$browserHeight={browserHeight - 88}
						$isDesktopFixed={isDesktopFixed}
						$isTab={isTab}
						ref={scrollRef}
					>
						{!loading && !clubs.length && <NotFound type="clubs-teams" />}
						{clubsLoading && <Spinner />}
						<ClubsAndTeams__List $isTab={isTab}>
							{clubs?.map((club) => {
								return (
									<ClubsAndTeams__Item key={club.roster_club_id}>
										<ClubsAndTeams__ItemInner>
											<ClubsAndTeams__Header>
												<img
													src={getClubIcon(club)}
													onClick={() => toggleClubTeamsFavorite(club)}
													alt=""
												/>
												{club.club_name}, {club.state}
											</ClubsAndTeams__Header>
										</ClubsAndTeams__ItemInner>
										<Teams
											openTeamModal={openTeamModal}
											openPoolModal={openPoolModal}
											openBracketModal={openBracketModal}
											club={club}
											favoriteTeamsIds={favoriteTeamsIds}
											toggleTeamFavorite={toggleTeamFavorite}
										/>
									</ClubsAndTeams__Item>
								);
							})}
						</ClubsAndTeams__List>
					</ClubsAndTeams__ListWrapper>
				</>
			</ClubsAndTeams__Wrapper>
		</>
	);
};

export default ClubsAndTeams;

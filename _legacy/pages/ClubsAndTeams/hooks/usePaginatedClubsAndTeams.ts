import { DEFAULT_PAGE_SIZE } from '@/config';
import { useCallback, useEffect, useMemo, useState } from 'react';

import {
	PaginatedClubsTeamsQuery,
	PaginatedClubsTeamsQueryVariables,
	usePaginatedClubsTeamsLazyQuery,
} from '@/generated/graphql';

type Params = Omit<PaginatedClubsTeamsQueryVariables, 'page' | 'pageSize'>;
type Club = PaginatedClubsTeamsQuery['paginatedClubs']['items'][number];

const INITIAL_PAGE = 1;

export const usePaginatedClubsAndTeams = (params: Params) => {
	const { eswId, search } = params;
	const [pagesResponses, setPagesResponses] = useState<PaginatedClubsTeamsQuery[]>([]);
	const [isFetched, setIsFetched] = useState(false);

	const [fetchClubsTeams, { data, loading }] = usePaginatedClubsTeamsLazyQuery();
	useEffect(() => {
		setPagesResponses([]);
		fetchClubsTeams({
			variables: {
				eswId,
				page: INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
			onCompleted: () => setIsFetched(true),
		});
	}, [fetchClubsTeams, search, eswId]);

	// Update the pagesResponses array when a new page is fetched taking care of the order
	useEffect(() => {
		if (!data) return;
		setPagesResponses((pagesResponses) => {
			const responses = [...pagesResponses];
			responses[data.paginatedClubs.page_info.page - 1] = data;
			return responses;
		});
	}, [data]);

	// Combine all clubs/teams from all pages
	const clubs = useMemo(() => {
		return pagesResponses.reduce<Club[]>((acc, response) => {
			if (!response) return acc;
			return [...acc, ...((response?.paginatedClubs?.items as Club[]) ?? [])];
		}, []);
	}, [pagesResponses]);
	const clubsCount = useMemo(() => {
		const lastResponse = pagesResponses.at(-1);
		return lastResponse?.paginatedClubs.page_info.item_count || 0;
	}, [pagesResponses]);

	// Prepare the fetchNext function
	const fetchNext = useCallback(() => {
		let nextPage;
		const lastResponse = pagesResponses.at(-1);
		if (lastResponse) {
			const { page, page_count } = lastResponse.paginatedClubs.page_info;
			if (page >= page_count) return;
			nextPage = page + 1;
		}
		fetchClubsTeams({
			variables: {
				eswId,
				page: nextPage || INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		});
	}, [pagesResponses, fetchClubsTeams, search, eswId]);

	return { fetchNext, clubs, clubsCount, loading, isFetched, pagesResponses };
};

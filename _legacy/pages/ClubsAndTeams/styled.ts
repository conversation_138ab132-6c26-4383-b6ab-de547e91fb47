import styled, { css } from 'styled-components';

export const ClubsAndTeams__Wrapper = styled.div``;
export const ClubsAndTeams__ListWrapper = styled.div<{
	$browserHeight: number;
	$isDesktopFixed: boolean;
	$isTab?: boolean;
}>`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: ${(props) => (props.$isTab ? '85px 24px 24px 24px' : '24px')};
		width: 680px;
		min-width: 648px;
		margin: auto;
		border-radius: 4px;
		background: #fff;
		min-height: ${(props) => props.$browserHeight - 304}px;
		box-shadow:
			0px 12px 24px -4px rgba(145, 158, 171, 0.12),
			0px 0px 2px 0px rgba(145, 158, 171, 0.2);
		position: fixed;
		left: 50%;
		margin: -48px 0 0 -340px;
		z-index: 99;
		top: 240px;
		overflow-y: auto;
		height: calc(100vh - 246px);
	}
`;
export const ClubsAndTeams__List = styled.ul<{ $isTab?: boolean }>`
	padding: ${(props) => (props.$isTab ? '0 0 66px' : '16px 16px 66px 16px')};
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		margin: auto;
		width: 600px;
		padding: 0;
	}
`;
export const ClubsAndTeams__Item = styled.li`
	border-radius: 4px;
	border: 1px solid #dfe3e8;
	margin: 0 0 32px;
	position: relative;
`;
export const ClubsAndTeams__ItemInner = styled.div`
	padding: 12px 49px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 12px 49px 2px 49px;
	}
`;
export const ClubsAndTeams__Header = styled.div`
	position: relative;
	font-size: 14px;
	font-weight: 700;

	img {
		position: absolute;
		left: -35px;
		top: -4px;
	}
`;
export const ClubsAndTeams__Code = styled.div`
	font-size: 14px;
	line-height: 22px;
`;
export const ClubsAndTeams__Count = styled.div`
	font-size: 14px;
	line-height: 18px;
	display: flex;
	align-items: center;
	cursor: pointer;
	img {
		padding: 0 0 0 8px;
	}
`;
export const ClubsAndTeams__Footer = styled.div<{ $isOpen?: boolean }>`
	display: flex;
	justify-content: space-between;
	padding: 0 16px 12px 49px;
	border-bottom: ${(props) => (props.$isOpen ? '1px solid #dfe3e8' : 'none')};
`;

const commonShadowStyles = css`
	height: 8px;
	border-radius: 0px 0px 2px 2px;
	position: absolute;
`;

export const ClubsAndTeams__ShadowTop = styled.div`
	${commonShadowStyles}
	background: #f6f6f6;
	bottom: -9px;
	left: 11px;
	width: calc(100% - 22px);
`;
export const ClubsAndTeams__ShadowBottom = styled.div`
	${commonShadowStyles}
	background: #f0f0f0;
	bottom: -17px;
	left: 17px;
	width: calc(100% - 34px);
`;
// * Teams
export const ClubsAndTeams__TeamWrapper = styled.div``;
export const ClubsAndTeams__TeamList = styled.ul`
	padding: 10px 4px 10px 23px;
`;
export const ClubsAndTeams__TeamItem = styled.li<{ $marginBottom?: boolean }>`
	display: flex;
	justify-content: space-between;
	align-self: center;
	gap: ${(props) => (props.$marginBottom ? 8 : 4)}px;
	margin-bottom: ${(props) => (props.$marginBottom ? 16 : 4)}px;
	min-height: 70px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		margin-bottom: 16px;
		gap: 16px;
	}
`;
export const ClubsAndTeams__TeamDescription = styled.div<{
	$isPlayed?: boolean;
	$isFavorite?: boolean;
}>`
	border-radius: 4px;
	border: 1px solid #dfe3e8;
	line-height: 22px;
	font-size: 14px;
	padding: 16px;
	width: 70%;
	display: flex;
	align-items: ${(props) => (props.$isPlayed ? 'center' : 'normal')};
	gap: 36px;
	background: ${(props) => (props.$isPlayed ? '#F4F6F8' : 'transparent')};
	padding-left: ${(props) => (props.$isFavorite ? '16px' : '16px')};
	strong {
		font-weight: 700;
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		cursor: pointer;
		transition: all 0.3s ease-in-out;
		&:hover {
			transition: all 0.3s ease-in-out;
			background: ${(props) => (props.$isPlayed ? '#edeff1' : 'transparent')};
			strong {
				text-decoration: underline;
			}
		}
	}
`;
export const ClubsAndTeams__TeamDescriptionCourtSection = styled.div<{ $isFavorite?: boolean }>`
	width: 62px;
	font-weight: 700;
	/* display: ${(props) => (props.$isFavorite ? 'none' : 'block')}; */
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-weight: 700;
		font-size: 16px;
	}
`;
export const ClubsAndTeams__TeamDescriptionInfoSection = styled.div`
	position: relative;
	width: 100%;
`;
export const ClubsAndTeams__TeamCourt = styled.div<{ $isPlayed?: boolean }>`
	border-radius: 4px;
	border: 1px solid #dfe3e8;
	font-size: 14px;
	line-height: 22px;
	padding: 8px;
	width: 30%;
	min-width: 120px;
	display: flex;
	align-items: center;
	flex-direction: row-reverse;
	background: ${(props) => (props.$isPlayed ? '#F4F6F8' : 'transparent')};
	text-align: right;
	p {
		width: 100%;
		text-align: ${(props) => (props.$isPlayed ? 'center' : 'right')};
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		cursor: pointer;
		transition: all 0.3s ease-in-out;
		&:hover {
			transition: all 0.3s ease-in-out;
			background: ${(props) => (props.$isPlayed ? '#edeff1' : 'transparent')};
			strong {
				text-decoration: underline;
			}
		}
	}
`;
export const ClubsAndTeams__FavoriteIcon = styled.img`
	position: absolute;
	left: -32px;
	top: 3px;
`;

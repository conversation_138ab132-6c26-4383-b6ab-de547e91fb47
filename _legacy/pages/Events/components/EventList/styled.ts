import styled from 'styled-components';

export const EventList__List = styled.ul`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: calc(680px - 48px);
		height: 100%;
		&::-webkit-scrollbar {
			width: 2px;
			background: transparent;
		}
	}
`;
export const EventList__Item = styled.li`
	padding: 16px;
	margin: 0 0 16px;
	border-top: 1px solid #dfe3e8;
	border-bottom: 1px solid #dfe3e8;
	&:last-child {
		margin: 0;
	}
	a {
		text-decoration: none;
		display: flex;
		justify-content: space-between;
		width: 100%;
	}

	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		border: 1px solid #dfe3e8;
		border-radius: 2px;
	}
`;
export const EventList__ItemName = styled.div`
	width: calc(100% - 50px);

	p {
		font-size: 14px;
		line-height: 22px;
		margin: 0 0 2px;
		&:first-child {
			font-weight: 700;
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
		}
	}
`;
export const EventList__Logo = styled.img`
	width: 36px;
	height: 36px;
`;
export const EventList__ItemLeftBox = styled.div`
	display: flex;
	gap: 18px;
	align-items: center;
	padding: 0 10px 0 0;
	width: calc(100% - 30px);
`;
export const EventList__ItemRightBox = styled.div``;

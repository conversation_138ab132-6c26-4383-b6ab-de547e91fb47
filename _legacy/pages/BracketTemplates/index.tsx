import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import axios from 'axios';
import { useEffect, useState } from 'react';

import { IBracketTemplate } from '@/components/BracketModal/types';
import { Loader } from '@/components/Loader';

import {
	BracketTemplates__Item,
	BracketTemplates__Link,
	BracketTemplates__List,
	BracketTemplates__MainTitle,
	BracketTemplates__Title,
	BracketTemplates__Wrapper,
} from './styled';

const BracketTemplates = () => {
	const { browserHeight } = useCurrentSize();
	const [bracketTemplates, setBracketTemplates] = useState<IBracketTemplate[]>([]);
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		const getTemplate = async () => {
			setIsLoading(true);
			const response = await axios.get(`${import.meta.env.VITE_BRACKETS_API_URL}/bracket`);
			setBracketTemplates(response.data);
			setIsLoading(false);
		};

		getTemplate();
	}, []);

	return (
		<>
			{isLoading && <Loader />}
			<BracketTemplates__Wrapper $browserHeight={browserHeight}>
				<BracketTemplates__MainTitle>Bracket Templates</BracketTemplates__MainTitle>
				<BracketTemplates__List data-testid="divisions-list">
					{bracketTemplates.map((bracket) => (
						<BracketTemplates__Item key={bracket.id}>
							<BracketTemplates__Link to={`/templates/${bracket.name}`}>
								<BracketTemplates__Title>{bracket.name}</BracketTemplates__Title>
							</BracketTemplates__Link>
						</BracketTemplates__Item>
					))}
				</BracketTemplates__List>
			</BracketTemplates__Wrapper>
		</>
	);
};

export default BracketTemplates;

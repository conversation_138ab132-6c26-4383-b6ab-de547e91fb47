import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { SelectOption } from '@/shared/types/select';
import { getTicketsDirectLink } from '@/utils';
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';

import buyAdmissionIcon from '@/assets/buy-admission-icon.svg';

import { Select } from '@/components/CustomSelect';
import { MainNav } from '@/components/MainNav';
import { SearchButton } from '@/components/SearchButton';
import { TimeRange } from '@/components/TimeRange';

import { convertTimeFormat } from '@/utils/time';

import { DivisionInfo } from '../../types';
import {
	SCROLL_OFFSET_TIME_RANGE,
	TimeRangeSchedule__BuyAdmission,
	TimeRangeSchedule__EventTitle,
	TimeRangeSchedule__EventTitleDescriptionWrapper,
	TimeRangeSchedule__Footer,
	TimeRangeSchedule__FooterWrapper,
	TimeRangeSchedule__Header,
	TimeRangeSchedule__MainNavWrapper,
	TimeRangeSchedule__TimeRangeWrapper,
	TimeRangeSchedule__Wrapper,
} from './styled';

type PropsT = {
	// FILTERS
	days: string[];
	selectedDay: string;
	onSelectDay: (_day: string) => void;
	selectedTimeRange: [string, string] | null;
	timeRangeMinMax: [string, string];
	onSelectTimeRange: (_timeRange: [string, string]) => void;
	timeSelectionStep: number;
	divisions: DivisionInfo[];
	selectedDivisionId: string;
	onSelectDivisionId: (_divisionId: string) => void;
	hideSelectors?: boolean;
	// SEARCH
	search: string;
	setSearch: Dispatch<SetStateAction<string>>;
	// FOCUSING AND NAVIGATION
	foundMatchesCount: number;
	navigateFoundMatches: (_type: 'prev' | 'next') => void;
	focusedMatchIndex: number;
	resetMatchFocus: () => void;
};
export const TimeRangeSchedule = ({
	days,
	selectedDay,
	onSelectDay,
	timeRangeMinMax,
	selectedTimeRange,
	onSelectTimeRange,
	timeSelectionStep,
	divisions,
	selectedDivisionId,
	onSelectDivisionId,
	hideSelectors,
	search,
	setSearch,
	foundMatchesCount,
	navigateFoundMatches,
	focusedMatchIndex,
	resetMatchFocus,
}: PropsT) => {
	const { event } = useEventDetails();
	const { id } = useParams();
	const isAssignedTickets = !!event?.is_require_recipient_name_for_each_ticket;
	const tickets_code = event?.tickets_code || '';
	const isShowBuyAdmission = !!event?.tickets_published;

	const daysOptions = useMemo<SelectOption[]>(
		() => days.map((d) => ({ key: convertTimeFormat(d, 'yyyy-MM-dd', 'eee MM/dd'), value: d })),
		[days],
	);

	const divisionsOptions = useMemo<SelectOption[]>(
		() =>
			divisions.map((d) => ({
				key: d.short_name!,
				value: d.division_id,
			})),
		[divisions],
	);

	const [isFixed, setIsFixed] = useState(window.scrollY > SCROLL_OFFSET_TIME_RANGE);

	// TODO move to share
	useEffect(() => {
		const handleScroll = () => {
			const newScrollTop = window.scrollY || document.documentElement.scrollTop;
			setIsFixed(newScrollTop > SCROLL_OFFSET_TIME_RANGE);
		};

		window.addEventListener('scroll', handleScroll);

		return () => {
			window.removeEventListener('scroll', handleScroll);
		};
	}, []);

	const { breakPont } = useCurrentSize();
	const isLarge = breakPont !== 'small';
	const { isDesktopFixed } = useDesktopSticky({ page: 'courtGrid' });
	return (
		<>
			<TimeRangeSchedule__Wrapper $isLarge={isLarge} className={isDesktopFixed ? 'sticky' : ''}>
				<TimeRangeSchedule__Header
					className={isLarge ? 'sticky-hide' : isDesktopFixed ? 'sticky-hide' : ''}
				>
					<TimeRangeSchedule__EventTitleDescriptionWrapper $fullWidth={!isShowBuyAdmission}>
						<TimeRangeSchedule__EventTitle>
							<Link to={`/events/${id}`}>{event?.long_name}</Link>
						</TimeRangeSchedule__EventTitle>
					</TimeRangeSchedule__EventTitleDescriptionWrapper>
					{isShowBuyAdmission && (
						<TimeRangeSchedule__BuyAdmission
							href={getTicketsDirectLink(tickets_code!, isAssignedTickets)}
							target="_blank"
						>
							<img src={buyAdmissionIcon} alt="buy admission" /> Buy admission
						</TimeRangeSchedule__BuyAdmission>
					)}
				</TimeRangeSchedule__Header>
				{isLarge && (
					<TimeRangeSchedule__MainNavWrapper>
						<MainNav />
						{isShowBuyAdmission && (
							<TimeRangeSchedule__BuyAdmission
								href={getTicketsDirectLink(tickets_code!, isAssignedTickets)}
								target="_blank"
							>
								<img src={buyAdmissionIcon} alt="buy admission" /> Buy admission
							</TimeRangeSchedule__BuyAdmission>
						)}
					</TimeRangeSchedule__MainNavWrapper>
				)}
				<TimeRangeSchedule__FooterWrapper>
					<TimeRangeSchedule__Footer $isFixed={isFixed} $isHidden={hideSelectors}>
						<Select
							width={90}
							value={selectedDay}
							options={daysOptions}
							onChange={onSelectDay as (_day: string | number) => void}
						/>
						<TimeRangeSchedule__TimeRangeWrapper>
							<TimeRange
								title="Court Grid Time Range"
								timeRangeMinMax={timeRangeMinMax}
								selectedTimeRange={selectedTimeRange}
								onSelectTimeRange={onSelectTimeRange}
								timeStep={timeSelectionStep}
							/>
						</TimeRangeSchedule__TimeRangeWrapper>
						<Select
							width={83}
							value={selectedDivisionId}
							options={divisionsOptions}
							onChange={onSelectDivisionId as (_divisionId: string | number) => void}
						/>
						<SearchButton
							currentResult={focusedMatchIndex}
							resultsCount={foundMatchesCount}
							navigateResults={navigateFoundMatches}
							search={search}
							setSearch={setSearch}
							onClose={resetMatchFocus}
						/>
					</TimeRangeSchedule__Footer>
				</TimeRangeSchedule__FooterWrapper>
			</TimeRangeSchedule__Wrapper>
		</>
	);
};

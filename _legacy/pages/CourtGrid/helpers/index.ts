import { currentUTCDay, parseUTCDay } from '@/utils/time';

import { ERROR_DELAY_THRESHOLD, WARNING_DELAY_THRESHOLD } from '../constants';
import { AlertType, GridMatch } from '../types';

export const getMatchAlert = ({ secs_end, secs_finished }: GridMatch): AlertType => {
	if (!secs_finished || !secs_end) return null;

	const diff = secs_finished - secs_end;
	if (diff < 0) {
		return null;
	} else {
		if (diff > ERROR_DELAY_THRESHOLD) {
			return 'error';
		}

		if (diff > WARNING_DELAY_THRESHOLD) {
			return 'warning';
		}
		return null;
	}
};

export const selectBestDay = (days: string[]): string => {
	const parsedDaysDates = days.map(parseUTCDay);
	const currentDayDate = currentUTCDay();

	if (currentDayDate <= parsedDaysDates[0]) return days[0];
	if (currentDayDate >= parsedDaysDates[parsedDaysDates.length - 1]) return days[days.length - 1];

	for (let i = 0; i < parsedDaysDates.length; i++) {
		if (parsedDaysDates[i].getTime() === currentDayDate.getTime()) return days[i];
		if (parsedDaysDates[i] > currentDayDate) return days[i];
	}

	return days[days.length - 1];
};

import styled, { css } from 'styled-components';

import { getCourtGridTypeAlert } from '@/styles/shared';

import { MultiSelect__Select } from '@/components/CustomMultiselect/styled';

export const Grid__TimeItem = styled.div`
	font-size: 12px;
	line-height: 18px;
	font-weight: 700;
	color: ${(props) => props.theme.colors.blue};
	span {
		font-size: 8px;
		line-height: 18px;
		font-weight: 700;
		color: #c4cdd5;
		margin: 0 0 0 6px;
	}
`;
const commonPrevNextStyles = css`
	border: none;
	background: transparent;
	cursor: pointer;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
`;
export const Grid__PrevBtn = styled.button`
	${commonPrevNextStyles}
	left: 0;
`;
export const Grid__NextBtn = styled.button`
	${commonPrevNextStyles}
	right: 0;
`;
export const GridWrapper = styled.div`
	position: fixed;
	width: 100%;
	height: 100%;
	padding-bottom: 50px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		position: static;
	}
`;
export const Grid__Wrapper = styled.div``;
export const Grid__Table = styled.table<{ $isDesktopFixed: boolean; $isFixedWidth?: boolean }>`
	min-height: 240px;
	width: ${({ $isFixedWidth }) => ($isFixedWidth ? 'auto' : '100%')};
	border-collapse: collapse;
	position: relative;
	thead {
		position: relative;
		height: 40px;
		z-index: 8;
		th {
			font-weight: normal;
			border-top: none;
			border-bottom: none;
			box-shadow: inset -1px -1px 0 #dfe3e8;
			border-left: none;
			border-right: none;
			@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
				border-bottom: 1px solid #dfe3e8;
			}
			&:first-child {
				max-width: 100px;
				height: 40px;
				border-left: none;
				border-right: none;
				text-align: left;
				border-left: 1px solid #dfe3e8;
				box-shadow: none;
				p {
					font-size: 14px;
					line-height: 22px;
					font-weight: 700;
					color: ${(props) => props.theme.colors.blue} !important;
					padding: 0;
					min-width: 70px;
				}
				img {
					/* right: -25px; */
				}
			}
			&:nth-child(2) {
				border-left: none;
			}
		}
	}
	th {
		min-width: ${(props) => (props.$isFixedWidth ? '105px' : '100px')};
		max-width: ${(props) => (props.$isFixedWidth ? '105px' : 'auto')};
		position: sticky;
		top: -1px;
		left: -1px;
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			top: -1px;
			left: -1px;
		}
		z-index: 9;
		background-color: #f2f7ff;
		&:first-child {
			min-width: 57px;
			position: sticky;
			top: -1px;
			left: -1px;
			z-index: 99;
		}
	}
	th,
	td {
		border: 1px solid #dfe3e8;

		&:last-child {
			border-right: none;
		}
	}
	tbody td {
		height: ${({ $isFixedWidth }) => ($isFixedWidth ? '148px' : '106px')};
		position: relative;
		&:first-child {
			border-right: none;
			border-left: none;
			z-index: 7;
			background: #fff;
			position: sticky;
			left: -1px;
			font-size: 12px;
			line-height: 18px;
			font-weight: 700;
			color: ${(props) => props.theme.colors.blue};
		}
		&:nth-child(2) {
			border-left: none !important;
		}
	}
	tbody {
		tr:first-child {
			td {
				border-top: none;
			}
		}
	}
	td:first-child {
		width: 57px;
	}
`;
export const Grid__TimeLine = styled.div<{ $width: number }>`
	text-align: left;
	span {
		display: inline-block;
		font-size: 8px;
		text-align: right;
		width: ${(props) => props.$width}px;
	}
`;
export const Grid__CourtWrapper = styled.div<{
	$type?: 'warning' | 'error' | null;
}>`
	border-right: 1px solid #dfe3e8;
	display: flex;
	height: 100%;
	align-items: center;
	border-left: 2px solid #dfe3e8;

	padding: 0 0 0 10px;
	color: ${(props) => getCourtGridTypeAlert(props.$type)};
	${MultiSelect__Select} {
		p {
			display: flex;
			align-items: center;
		}
	}
`;
export const Grid__MainContent = styled.div`
	overflow: auto;
	overscroll-behavior: none;
	-webkit-overflow-scrolling: touch;
	height: calc(100% - 120px);
	position: relative;
	top: 115px;
	left: -1px;
	min-height: 50vh;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		max-width: 1160px;
		width: fit-content;
		top: 140px;
		background: #fff;
		margin: auto;
		max-height: calc(100vh - 275px);
	}
`;
export const Grid__TableWrapper = styled.div<{ $isDesktopFixed: boolean; $isEmpty?: boolean }>`
	border-right: 1px solid #dfe3e8;
	border-bottom: 1px solid #dfe3e8;
	width: fit-content;
	border: ${(props) => props.$isEmpty && 'none'};
	${(props) => props.$isEmpty && `height: 100vh;`};
`;
export const Grid__TimeRangeWrapper = styled.div`
	position: fixed;
	width: 100%;
	z-index: 999;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		z-index: 9;
	}
`;
export const Grid__TableTopLine = styled.div<{ $periodsLength: number }>`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: ${(props) => props.$periodsLength * 105 + 82}px;
		max-width: 1160px;
		height: 1px;
		background: #dfe3e8;
		position: fixed;
		top: 201px;
	}
`;

export const Grid__EmptyValue = styled.div`
	color: #637381;
	text-align: center;
	font-size: 16px;
	line-height: 24px;
	padding-top: 100px;
`;

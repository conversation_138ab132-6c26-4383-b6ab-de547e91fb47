import { useMemo } from 'react';

import { ALL_DIVISIONS_KEY } from '../constants';
import { useCourtsMatches } from './useCourtsMatches';
import { useMatchesTimeGrid } from './useMatchesTimeGrid';

export const useCourtGrid = (
	day: string,
	timeRange: [string, string] | null,
	divisionId: string,
	selectedCourts: string[],
) => {
	// Getting the data
	const { courtsMatches, hasMoreCourtsMatches, courtsMatchesLoading } = useCourtsMatches(
		day,
		timeRange,
		divisionId === ALL_DIVISIONS_KEY ? null : divisionId,
	);

	const selectedCourtsMatches = useMemo(() => {
		if (!selectedCourts.length) {
			return courtsMatches;
		}
		return courtsMatches.filter(({ court }) => selectedCourts.includes(court.uuid));
	}, [selectedCourts, courtsMatches]);

	const [gridTimesPeriods, gridTimeStep] = useMatchesTimeGrid(selectedCourtsMatches);

	return {
		allCourtsMatches: courtsMatches,
		selectedCourtsMatches,
		courtsMatchesLoading,
		gridTimesPeriods,
		gridTimeStep,
		hasMoreCourtsMatches,
	};
};

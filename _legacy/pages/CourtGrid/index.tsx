import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useBracketModal } from '@/shared/hooks/useBracketModal';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { usePoolModal } from '@/shared/hooks/usePoolModal';
import { useTeamModal } from '@/shared/hooks/useTeamModal';
import { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

import { CustomMultiSelect } from '@/components/CustomMultiselect';
import { Loader } from '@/components/Loader';
import { NotFound } from '@/components/NotFound';
import { Spinner } from '@/components/Spinner';

import { GridCard } from './components/GridCard';
import { TimeRangeSchedule } from './components/TimeRangeSchedule';
import { TIME_SELECTION_STEP } from './constants';
import { useCourtGrid } from './hooks/useCourtGrid';
import { useCourtGridFilter } from './hooks/useCourtGridFilter';
import { useCourtGridSearch } from './hooks/useCourtGridSearch';
import {
	GridWrapper,
	Grid__CourtWrapper,
	Grid__MainContent,
	Grid__Table,
	Grid__TableTopLine,
	Grid__TableWrapper,
	Grid__TimeItem,
	Grid__TimeRangeWrapper,
	Grid__Wrapper,
} from './styled';

const CourtGrid = () => {
	const {
		divisions,
		divisionId,
		setDivisionId,
		days,
		day,
		setDay,
		timeRangeMinMax,
		timeRange,
		setTimeRange,
		selectedCourts,
		setSelectedCourts,
		resetFilter,
		filterReady,
	} = useCourtGridFilter();

	const {
		allCourtsMatches,
		selectedCourtsMatches,
		courtsMatchesLoading,
		gridTimesPeriods,
		gridTimeStep,
		hasMoreCourtsMatches,
	} = useCourtGrid(day, timeRange, divisionId, selectedCourts);

	const {
		search,
		setSearch,
		foundMatchesCount,
		focusedMatchId,
		focusedMatchIndex,
		navigateFoundMatches,
		resetMatchFocus,
	} = useCourtGridSearch(selectedCourtsMatches);

	useAnalytics('Court Grid', search);

	const { isDesktopFixed } = useDesktopSticky({ page: 'courtGrid' });

	const isLoading = !filterReady || courtsMatchesLoading;
	const gridWrapperRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		document.body.style.overflow = 'hidden';
		gridWrapperRef.current?.scrollTo(1, 0);
		return () => {
			document.body.style.overflow = 'unset';
		};
	}, []);
	const { breakPont } = useCurrentSize();
	const isMobile = breakPont === 'small';
	const scrollHandler = (e: React.UIEvent<HTMLDivElement>) => {
		const target = e.currentTarget;
		if (target.scrollLeft < 1) {
			target.scrollLeft = 1;
		}
	};

	const portalWrapper = (modal: JSX.Element) => {
		return createPortal(<>{modal}</>, document.getElementById('portal')!);
	};

	const { poolModalElement, openPoolModal } = usePoolModal();
	const { bracketModalElement, openBracketModal } = useBracketModal(openPoolModal);

	const { teamModalElement, openTeamModal } = useTeamModal();
	const renderGridTableWrapper = () => {
		return (
			<Grid__MainContent>
				<Grid__TableWrapper
					$isDesktopFixed={isDesktopFixed}
					onScroll={!isMobile ? scrollHandler : undefined}
					ref={gridWrapperRef}
				>
					{teamModalElement && portalWrapper(teamModalElement)}
					{bracketModalElement && portalWrapper(bracketModalElement)}
					{poolModalElement && portalWrapper(poolModalElement)}
					<Grid__TableTopLine $periodsLength={gridTimesPeriods.length} />
					<Grid__Table $isDesktopFixed={isDesktopFixed} $isFixedWidth>
						<thead>
							<tr>
								<th>
									<Grid__CourtWrapper>
										<CustomMultiSelect
											data={allCourtsMatches.map(({ court }) => ({
												key: `${court.name} - (Ct ${court.short_name})`,
												value: court.uuid,
											}))}
											type="courtGrid"
											selectName="Ct"
											dropDownName="Select courts"
											setSelectedData={setSelectedCourts}
											initialSelectedData={selectedCourts}
										/>
									</Grid__CourtWrapper>
								</th>
								{gridTimesPeriods.map(({ periodStart, periodDisplayName }) => {
									return (
										<th key={`thead_${periodStart}`}>
											<Grid__TimeItem>{periodDisplayName}</Grid__TimeItem>
										</th>
									);
								})}
							</tr>
						</thead>
						<tbody>
							{selectedCourtsMatches.map(({ court, matches, courtAlert, courtTbMatchesCount }) => {
								return (
									<tr key={court.uuid}>
										<td>
											<Grid__CourtWrapper $type={courtAlert}>
												Ct {court.short_name}
											</Grid__CourtWrapper>
										</td>
										{gridTimesPeriods.map(({ periodStart, periodEnd }, index) => {
											return (
												<td
													key={`tbody_${periodStart}_${index}`}
													style={{ height: courtTbMatchesCount && courtTbMatchesCount * 96 }}
												>
													{matches.map((match) => {
														const { secs_start, secs_end } = match;
														// If match is not in current period or has no start/end time
														if (
															!(secs_start! >= periodStart && secs_start! < periodEnd && secs_end)
														) {
															return null;
														}

														return (
															<GridCard
																key={match.match_id}
																match={match}
																focusedMatchId={focusedMatchId}
																minPeriod={gridTimeStep}
																tbMatchesCount={courtTbMatchesCount}
																openTeamModal={openTeamModal}
																openPoolModal={openPoolModal}
																openBracketModal={openBracketModal}
																rowsCount={selectedCourtsMatches.length}
															/>
														);
													})}
												</td>
											);
										})}
									</tr>
								);
							})}
						</tbody>
					</Grid__Table>
				</Grid__TableWrapper>
			</Grid__MainContent>
		);
	};

	return (
		<Grid__Wrapper>
			{isLoading && <Loader />}
			{courtsMatchesLoading && <Spinner />}
			<Grid__TimeRangeWrapper>
				<TimeRangeSchedule
					days={days}
					selectedDay={day}
					onSelectDay={setDay}
					timeRangeMinMax={timeRangeMinMax}
					selectedTimeRange={timeRange}
					onSelectTimeRange={setTimeRange}
					timeSelectionStep={TIME_SELECTION_STEP}
					divisions={divisions}
					selectedDivisionId={divisionId}
					onSelectDivisionId={setDivisionId}
					search={search}
					setSearch={setSearch}
					foundMatchesCount={foundMatchesCount}
					navigateFoundMatches={navigateFoundMatches}
					focusedMatchIndex={focusedMatchIndex}
					resetMatchFocus={resetMatchFocus}
					hideSelectors={!filterReady}
				/>
			</Grid__TimeRangeWrapper>
			{gridTimesPeriods.length ? (
				<GridWrapper>{renderGridTableWrapper()}</GridWrapper>
			) : (
				!isLoading &&
				(hasMoreCourtsMatches ? (
					<Grid__MainContent>
						<Grid__TableWrapper $isDesktopFixed={isDesktopFixed} $isEmpty>
							<NotFound type="court-grid" onClick={resetFilter} />
						</Grid__TableWrapper>
					</Grid__MainContent>
				) : (
					<NotFound type="court-grid-empty" />
				))
			)}
		</Grid__Wrapper>
	);
};

export default CourtGrid;

import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useMemo } from 'react';
import { useState } from 'react';

import { useFavoriteTeamsStandingQuery } from '@/generated/graphql';

export const useFavoriteTeamsStanding = (teamsIds: string[], search: string) => {
	const { eswId } = useEventDetails();

	// Limit teams to 200 to avoid query error
	const limitedTeamsIds = useMemo(() => teamsIds.slice(0, 200), [teamsIds]);

	const [isFetched, setIsFetched] = useState(false);
	const { data, previousData, loading } = useFavoriteTeamsStandingQuery({
		skip: !teamsIds.length,
		variables: {
			eswId,
			teamsIds: limitedTeamsIds,
			search,
		},
		onCompleted: () => setIsFetched(true),
	});

	const teams = useMemo(() => {
		if (!teamsIds.length) {
			setIsFetched(true);
			return [];
		}
		return data?.favoriteTeams || previousData?.favoriteTeams || [];
	}, [teamsIds, data, previousData]);

	return { teams, loading, isFetched };
};

import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { useEventDescription } from '@/shared/hooks/useEventDescription';
import { useEventDetails } from '@/shared/hooks/useEventDetails';

import { SearchBar } from '@/components/SearchBar';
import { Tabs } from '@/components/Tabs';

import { placeholders } from '@/config/searchPlaceholder';

import { useFavorites } from './hooks/useFavorites';
import { Favorites__Wrapper } from './styled';

type Props = {
	isTab?: boolean;
};

const Favorites = ({ isTab }: Props) => {
	const { event } = useEventDetails();
	const description = useEventDescription();

	const { search, onChangeSearch, activeTab, setActiveTab, tabs } = useFavorites(isTab);
	useAnalytics(`Favorites (${activeTab})`, search);

	const { browserHeight } = useCurrentSize();
	const { isDesktopFixed } = useDesktopSticky({ page: 'favorites' });

	return (
		<>
			{!isTab && (
				<SearchBar
					placeholder={
						activeTab === 'Teams' ? placeholders.favorites_standings : placeholders.favorites_teams
					}
					eventName={event?.long_name || ''}
					description={description}
					search={search}
					onChangeSearch={onChangeSearch}
					isShowBuyAdmission={!!event?.tickets_published}
				/>
			)}
			<Favorites__Wrapper
				$browserHeight={browserHeight}
				$isDesktopFixed={isDesktopFixed}
				$isTab={isTab}
			>
				<Tabs activeTab={activeTab} handleChange={(_e, value) => setActiveTab(value)} tabs={tabs} />
			</Favorites__Wrapper>
		</>
	);
};

export default Favorites;

import styled from 'styled-components';

export const About__Wrapper = styled.div<{ $browserHeight: number; $isDesktopFixed: boolean }>`
	background: #f9fafb;

	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 24px;
		width: 680px;
		min-width: 648px;
		margin: auto;
		border-radius: 4px;
		background: #fff;
		min-height: ${(props) => props.$browserHeight - 304}px;
		box-shadow:
			0px 12px 24px -4px rgba(145, 158, 171, 0.12),
			0px 0px 2px 0px rgba(145, 158, 171, 0.2);
		position: fixed;
		left: 50%;
		margin: -48px 0 0 -340px;
		z-index: 99;
		top: 240px;
		overflow-y: auto;
		height: calc(100vh - 246px);
	}
`;
export const About__Inner = styled.div`
	padding: 0 16px 16px 16px;

	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		max-width: 600px;
		margin: auto;
		padding: 0;
	}
`;

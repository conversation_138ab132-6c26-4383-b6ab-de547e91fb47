import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useFavoriteTeams } from '@/shared/hooks/useFavoriteTeams';
import { sanitizeSearchInput } from '@/utils';
import { KeyboardEvent, useCallback, useMemo, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { StringParam, useQueryParams } from 'use-query-params';

import { useEventCountsQuery } from '@/generated/graphql';

import { MAIN_NAV, NavT } from '../constants';

export const useAbout = () => {
	const { eswId, event, divisions, loading: eventDetailsLoading } = useEventDetails();
	const { favoriteTeamsIds } = useFavoriteTeams();

	const { data: eventCounts, loading: eventCountsLoading } = useEventCountsQuery({
		variables: {
			eswId,
		},
	});

	const loading = eventDetailsLoading || eventCountsLoading;

	const [searchParams, setQueryParams] = useQueryParams({
		search: StringParam,
	});

	const [search, setSearch] = useState(searchParams.search || '');
	const onChangeSearch = useCallback((value: string) => {
		setSearch(value);
	}, []);

	const nav = useMemo(() => {
		// If the is no event or the schedule is not published, return an empty nav
		if (!event?.schedule_published) return [] as NavT;
		return MAIN_NAV.reduce<NavT>((acc, item) => {
			let count = '';
			if (item.key === 'favorites') {
				count = `${favoriteTeamsIds.length}`;
			}
			if (item.key === 'clubs-teams') {
				if (!eventCounts || !divisions) return acc;
				const clubsCount = eventCounts.paginatedClubs.page_info.item_count;
				const teamsCount = divisions.reduce((acc, division) => acc + division.teams_count, 0);
				count = `${clubsCount}/${teamsCount}`;
			}
			if (item.key === 'athletes') {
				if (!eventCounts || !event.has_rosters) return acc;
				const athletesCount = eventCounts.paginatedAthletes.page_info.item_count;
				const staffCount = eventCounts.paginatedStaff.page_info.item_count;
				count = `${athletesCount + staffCount}`;
			}
			if (item.key === 'divisions') {
				if (!divisions?.length) return acc;
				count = `${divisions.length}`;
			}
			if (item.key === 'previously-qualified' && !event.is_with_prev_qual) {
				return acc;
			}
			acc.push({
				...item,
				count,
				path: `/events/${eswId}/${item.path}`,
			});
			return acc;
		}, []);
	}, [event, divisions, eventCounts, favoriteTeamsIds.length, eswId]);

	const navigate = useNavigate();
	const location = useLocation();

	const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
		const sanitizedSearch = sanitizeSearchInput(search);
		if (!sanitizedSearch) return;
		if (event.key === 'Enter' && location.pathname === `/events/${eswId}/about`) {
			setQueryParams({ search: sanitizedSearch });
			navigate(`/events/${eswId}/search-result/best?search=${encodeURIComponent(sanitizedSearch)}`);
		}
	};

	return {
		nav,
		loading,
		search,
		onChangeSearch,
		handleKeyDown,
	};
};

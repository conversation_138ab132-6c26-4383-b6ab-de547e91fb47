import { Link } from 'react-router-dom';
import styled from 'styled-components';

export const EventNav__List = styled.ul`
	width: 100%;
	padding-top: 16px;
	margin-bottom: 0;
	&:not(:empty) {
		margin-bottom: 16px;
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding-top: 0;
	}
`;
export const EventNav__Item = styled.li`
	margin: 0 0 8px;
	background: #fff;
	box-shadow: 0px 12px 24px -4px #919eab29;
	border: 1px solid #919eab29;
	border-radius: 4px;

	&:last-child {
		margin-bottom: 0;
	}
`;
export const EventNav__Title = styled(Link)`
	font-size: 14px;
	line-height: 18px;
	text-decoration: none;
	display: flex;
	justify-content: space-between;
	padding: 16px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 14px;
		line-height: 22px;
	}
`;
export const EventNav__TitleContent = styled.div`
	display: flex;
	align-items: center;
	gap: 5px;
`;
export const EventNav__TitleStream = styled.a`
	font-size: 14px;
	line-height: 18px;
	display: flex;
	align-items: center;
	gap: 5px;
	text-decoration: none;
	padding: 16px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 14px;
		line-height: 22px;
	}
`;
export const EventNav__Count = styled.span`
	color: #637381;
	font-size: 14px;
	line-height: 18px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 14px;
		line-height: 22px;
	}
`;

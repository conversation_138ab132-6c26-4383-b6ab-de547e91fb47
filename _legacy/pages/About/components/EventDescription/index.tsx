import { useEventDetails } from '@/shared/hooks/useEventDetails';

import {
	EventDescription__Content,
	EventDescription__Description,
	EventDescription__Footer,
	EventDescription__FooterItem,
	EventDescription__FooterList,
	EventDescription__FooterTitle,
	EventDescription__Header,
	EventDescription__Wrapper,
} from './styled';

export const EventDescription = () => {
	const { event } = useEventDetails();
	const notes = event?.event_notes || '';
	const state = event?.state || '';
	const city = event?.city || '';
	const address = event?.address || '';
	const location = event?.locations?.[0]?.location_name || '';

	return (
		<EventDescription__Wrapper>
			<EventDescription__Header>About event</EventDescription__Header>
			<EventDescription__Content>
				<EventDescription__Description dangerouslySetInnerHTML={{ __html: notes }} />
				<EventDescription__Footer>
					<EventDescription__FooterTitle>Event Location:</EventDescription__FooterTitle>
					<EventDescription__FooterList>
						<EventDescription__FooterItem>
							<span>State:</span>
							<span>{state}</span>
						</EventDescription__FooterItem>
						<EventDescription__FooterItem>
							<span>City:</span>
							<span>{city}</span>
						</EventDescription__FooterItem>
						<EventDescription__FooterItem>
							<span>Address:</span>
							<span>{address}</span>
						</EventDescription__FooterItem>
						<EventDescription__FooterItem>
							<span>Location:</span>
							<span>{location}</span>
						</EventDescription__FooterItem>
					</EventDescription__FooterList>
				</EventDescription__Footer>
			</EventDescription__Content>
		</EventDescription__Wrapper>
	);
};
